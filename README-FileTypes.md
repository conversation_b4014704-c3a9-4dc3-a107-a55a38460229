# File Types Configuration

To enable the app to handle custom file types, you need to update the Info.plist file with the following configuration:

1. Open the Info.plist file in Xcode
2. Add the following entries:

## Document Types

Add a new key: `CFBundleDocumentTypes` (Array)
Add an item with the following properties:

```
CFBundleTypeName: PoleVaultLogPro JSON
LSHandlerRank: Owner
LSItemContentTypes: ["com.drewbrumm.polevaultlogpro.json"]
```

## Exported UTI Types

Add a new key: `UTExportedTypeDeclarations` (Array)
Add an item with the following properties:

```
UTTypeConformsTo: ["public.json"]
UTTypeDescription: PoleVaultLogPro JSON
UTTypeIdentifier: com.drewbrumm.polevaultlogpro.json
UTTypeTagSpecification:
    public.filename-extension: ["pvl.json"]
    public.mime-type: ["application/json"]
```

## Imported UTI Types

Add a new key: `UTImportedTypeDeclarations` (Array)
Add an item with the following properties:

```
UTTypeConformsTo: ["public.data"]
UTTypeDescription: Excel Spreadsheet
UTTypeIdentifier: org.openxmlformats.spreadsheetml.sheet
UTTypeTagSpecification:
    public.filename-extension: ["xlsx"]
    public.mime-type: ["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"]
```

After making these changes, the app will be able to handle .pvl.json files and .xlsx files properly.

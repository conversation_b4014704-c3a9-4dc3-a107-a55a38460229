# File Types Configuration - V1.0

This document describes the file type configuration for PoleVaultLogPro V1.0's import/export functionality.

## V1.0 Supported File Types

### Import
- ✅ `.pvl.json` - PoleVaultLogPro JSON export files

### Export
- ✅ `.pvl.json` - Complete data export in JSON format
- ✅ `.csv` - Spreadsheet-compatible export format

## Info.plist Configuration

The following configuration is already implemented in the V1.0 app:

### Document Types
```xml
<key>CFBundleDocumentTypes</key>
<array>
    <dict>
        <key>CFBundleTypeName</key>
        <string>PoleVaultLogPro JSON</string>
        <key>LSHandlerRank</key>
        <string>Owner</string>
        <key>LSItemContentTypes</key>
        <array>
            <string>com.brummblebee.polevaultlogpro.json</string>
        </array>
    </dict>
</array>
```

### Exported UTI Types
```xml
<key>UTExportedTypeDeclarations</key>
<array>
    <dict>
        <key>UTTypeConformsTo</key>
        <array>
            <string>public.json</string>
        </array>
        <key>UTTypeDescription</key>
        <string>PoleVaultLogPro JSON</string>
        <key>UTTypeIdentifier</key>
        <string>com.brummblebee.polevaultlogpro.json</string>
        <key>UTTypeTagSpecification</key>
        <dict>
            <key>public.filename-extension</key>
            <array>
                <string>pvl.json</string>
            </array>
            <key>public.mime-type</key>
            <array>
                <string>application/json</string>
            </array>
        </dict>
    </dict>
</array>
```

## File Handling Implementation

### Export Process
1. User selects export from Settings or Session detail
2. App generates temporary file with appropriate UTType
3. iOS share sheet presents sharing options
4. File is shared via user's chosen method

### Import Process
1. User selects .pvl.json file from Files app or other source
2. App validates JSON structure and content
3. Data is imported with duplicate detection
4. User receives feedback on import results

## Removed from V1.0
- ❌ Excel (.xlsx) file support
- ❌ ZIP archive handling
- ❌ Custom file format validation beyond JSON

The V1.0 implementation focuses on reliable JSON-based data interchange with CSV export for analysis purposes.

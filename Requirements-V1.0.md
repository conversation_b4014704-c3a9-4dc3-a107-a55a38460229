# PoleVaultLogPro – V1.0 Production Requirements

## Product Vision
A sleek, lightning-fast logbook for pole vaulters that captures every jump, technical detail, note, and media in an intuitive interface; displays both metric & imperial units (preferred unit first and bold); lets athletes track technical measurements; visualizes progress; and supports full data ownership and export with CloudKit synchronization.

## Data Model (Core Data v4)
All linear distances are persisted in **centimeters** for consistency.

### Core Entities

#### Athlete
- `id` (String): Unique identifier
- `name` (String): Athlete's name  
- `dateOfBirth` (Date): Date of birth
- `dominantHand` (String): "left" or "right"
- `personalBestCm` (Double): Personal best in centimeters
- `personalBestIn` (Double): Personal best in inches
- **Relationships**: sessions (one-to-many), poles (one-to-many)

#### Session
- `id` (String): Unique identifier
- `date` (Date): Session date (stored as TimeInterval)
- `type` (String): "practice" or "meet"
- `title` (String): Session title
- `location` (String): Location name
- `weather` (String): Weather conditions
- `notesAthlete` (String): Athlete's notes
- `notesCoach` (String): Coach's notes
- `bestHeightCm` (Double): Best height achieved in session
- `bestHeightIn` (Double): Best height in inches
- **Relationships**: athlete (many-to-one), jumps (one-to-many), gridHeights (one-to-many)

#### Jump
- `id` (String): Unique identifier
- `order` (Int16): Order within session
- `columnIndex` (Int16): Grid column index
- `attemptIndex` (Int16): Attempt number (1-3)
- `barHeightCm` (Double): Bar height in centimeters
- `result` (String): "make", "miss", or "pass"
- `resultCode` (String): "O", "X", or "P"
- `runStartCm` (Double): Run start distance
- `handHoldCm` (Double): Hand hold height
- `takeOffStepCm` (Double): Take-off step distance
- `standardCm` (Double): Standard distance
- `comment` (String): Jump comment
- `useBar` (Bool): True for bar, false for bungee
- **Relationships**: session (many-to-one), pole (many-to-one), mediaItems (one-to-many)

#### JumpMedia
- `id` (String): Unique identifier
- `type` (String): "photo" or "video"
- `assetIdentifier` (String): PHAsset local identifier
- `posterTime` (Double): Video poster time (0.0-1.0)
- **Relationships**: jump (many-to-one)

#### Pole
- `id` (String): Unique identifier
- `name` (String): Pole name/model
- `lengthCm` (Double): Length in centimeters
- `weightGrams` (Double): Weight in grams
- `stiffnessRating` (String): Stiffness rating
- **Relationships**: athlete (many-to-one), jumps (one-to-many)

#### GridHeight
- `id` (String): Unique identifier
- `heightCm` (Double): Height in centimeters
- `order` (Int16): Order in grid
- **Relationships**: session (many-to-one)

## Core Features Implemented

### Navigation Structure
- **Tab-based navigation** with 6 tabs:
  1. **Log**: Session list and management
  2. **Dashboard**: Analytics and statistics
  3. **History**: Historical session view
  4. **Media**: Media browser for all jump media
  5. **Converter**: Height conversion tool
  6. **Settings**: App configuration

### Session Management
- Create Practice and Meet sessions
- Edit session details (title, date, location, notes)
- Session list with statistics display
- Session detail view with jump grid
- Delete sessions with confirmation

### Jump Tracking
- Grid-based jump entry interface
- Record bar height, result (make/miss/pass), attempts
- Technical measurements: run start, hand hold, take-off step, standard
- Bar/bungee toggle for each jump
- Jump editing and deletion
- Recent jumps view with chronological ordering

### Media Management
- Attach photos and videos to jumps using PHPicker
- Store media as PHAsset identifiers (no local copies)
- Media browser with full-screen viewing
- Video playback with AVPlayer
- Photo viewing with zoom support
- Media thumbnails in jump lists
- Standard iOS sharing for media (UIActivityViewController)

### Measurement System
- Dual unit support: metric (meters/cm) and imperial (feet/inches)
- User preference for primary unit display
- Secondary units shown with primary units
- Height converter tool
- Consistent formatting throughout app

### Data Export/Import
- **Export single session**: JSON (.pvl.json) and CSV formats
- **Export all data**: JSON and CSV formats
- **Import data**: From .pvl.json files with duplicate detection
- File sharing via iOS share sheet
- Complete data preservation in exports

### Analytics Dashboard
- Personal best tracking with celebration
- Success rate statistics (make/miss/pass percentages)
- Time-based filtering (week, month, year, all time)
- Visual charts and statistics
- Performance trend visualization

### Settings & Customization
- **Units**: Metric/Imperial preference toggle
- **App Icon**: Male/female pole vaulter icon selection
- **Theme**: Custom accent color picker
- **Athlete Profile**: Name, date of birth, dominant hand, personal best
- **Data Management**: Export, import, reset functionality
- **Storage**: Media storage usage display and management

### CloudKit Integration
- Automatic data synchronization across devices
- iCloud key-value store for user preferences
- Offline-first operation with sync when available
- CloudKit diagnostics and status monitoring

## Technical Implementation

### Architecture
- **SwiftUI** for user interface
- **Core Data** with CloudKit for data persistence
- **Combine** for reactive programming
- **PhotosUI/Photos** for media handling
- **AVKit/AVFoundation** for video playback

### Media Storage
- PHAsset identifiers stored in Core Data
- No local file copies (iCloud-based)
- Thumbnail generation and caching
- SharedMediaManager for cross-device media access

### Performance
- Efficient Core Data queries with fetch request helpers
- Lazy loading of media content
- Background processing for exports
- Optimized UI updates with @FetchRequest

## What's NOT Implemented (Removed from V1.0)
- ❌ Video processing and watermark overlays
- ❌ Complex video export with custom overlays
- ❌ Meet height standards (NFHS/FHSAA progression)
- ❌ Height picker with official meet standards
- ❌ Measurement range configuration
- ❌ Advanced video sharing features
- ❌ Biometric authentication
- ❌ Excel (.xlsx) export (only CSV)
- ❌ ZIP archive exports
- ❌ Custom meet standards management

## App Store Ready Features
- Complete Core Data model with CloudKit sync
- Full CRUD operations for all entities
- Media attachment and sharing
- Data export/import functionality
- Professional UI with consistent design
- Error handling and user feedback
- Offline operation capability
- iCloud synchronization

This represents the complete, production-ready V1.0 feature set as implemented in the codebase.

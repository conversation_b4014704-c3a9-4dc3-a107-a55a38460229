//
//  VideoProcessor.swift
//  PoleVaultLogPro
//
//  Drop-in helper to burn static text overlays into videos by
//  stacking a 1-frame transparent overlay track over the source track.
//  No CoreAnimation, no per-frame compositing – fully hardware-accelerated.
//

import Foundation
@preconcurrency import AVFoundation
import Photos
import UIKit
import CoreMedia
import CoreGraphics

/// A service class for processing videos with overlays using two-track composition
@MainActor
class VideoProcessor: ObservableObject {

    // MARK: - Published Properties
    @Published var isProcessing = false
    @Published var currentProgress: Float = 0.0

    // MARK: - Private Properties
    private var currentExportSession: AVAssetExportSession?
    private var currentExportTask: Task<Void, Never>?

    // MARK: - Error Types
    enum ProcessorError: Error, LocalizedError {
        case assetNotFound
        case permissionDenied
        case unableToCreateExportSession
        case exportFailed(Error)
        case cancelled
        case invalidPreset
        case fileError(String)

        var errorDescription: String? {
            switch self {
            case .assetNotFound:
                return "Video asset not found"
            case .permissionDenied:
                return "Photos access permission denied"
            case .unableToCreateExportSession:
                return "Unable to create export session"
            case .exportFailed(let error):
                return "Export failed: \(error.localizedDescription)"
            case .cancelled:
                return "Export was cancelled"
            case .invalidPreset:
                return "Invalid export preset"
            case .fileError(let message):
                return message
            }
        }
    }

    // MARK: - Initialization
    static let shared = VideoProcessor()

    private init() {
        print("📹 DEBUG: VideoProcessor initialized")
    }

    deinit {
        // Clean up any ongoing operations
        currentExportSession?.cancelExport()
        currentExportTask?.cancel()
    }

    // MARK: - Video Quality Presets
    enum VideoQualityPreset: String {
        case highest = "AVAssetExportPresetHighestQuality"
        case medium = "AVAssetExportPreset1920x1080"
        case low = "AVAssetExportPreset1280x720"
        case auto

        var avPreset: String {
            switch self {
            case .highest:
                return AVAssetExportPresetHighestQuality
            case .medium:
                return AVAssetExportPreset1920x1080
            case .low:
                return AVAssetExportPreset1280x720
            case .auto:
                return VideoQualityPreset.defaultPreset().avPreset
            }
        }

        static func defaultPreset() -> VideoQualityPreset {
            if #available(iOS 18.0, *) { return .highest }   // A18+
            return .medium                                   // A11-A17
        }
    }

    /// Options for video export
    struct VideoExportOptions {
        var includeJumpStats: Bool = true
        var includeSessionInfo: Bool = true
        var includeAppWatermark: Bool = true
        var quality: VideoQualityPreset = .auto
        var outputFileType: AVFileType = .mp4

        /// Validates that the export preset supports the output file type
        var isValid: Bool {
            // Ensure MP4 is supported for the selected preset
            guard outputFileType == .mp4 else { return false }
            return true
        }

        /// Preset for highest quality export
        static let highQuality = VideoExportOptions(
            includeJumpStats: true,
            includeSessionInfo: true,
            includeAppWatermark: true,
            quality: .highest,
            outputFileType: .mp4
        )

        /// Preset for medium quality export (faster)
        static let mediumQuality = VideoExportOptions(
            includeJumpStats: true,
            includeSessionInfo: true,
            includeAppWatermark: true,
            quality: .medium,
            outputFileType: .mp4
        )

        /// Preset for simple export (no overlays, for testing)
        static let simpleExport = VideoExportOptions(
            includeJumpStats: false,
            includeSessionInfo: false,
            includeAppWatermark: false,
            quality: .medium,
            outputFileType: .mp4
        )
    }

    // MARK: - Cancellation Support

    /// Cancels the current video processing operation
    func cancelCurrentProcessing() {
        print("📹 DEBUG: Cancelling current video processing")

        // Cancel the current task
        currentExportTask?.cancel()
        currentExportTask = nil

        // Cancel the export session
        currentExportSession?.cancelExport()
        currentExportSession = nil

        // Reset state
        Task { @MainActor in
            self.isProcessing = false
            self.currentProgress = 0.0
        }
    }

    // MARK: - File Management

    /// Moves a file from temporary directory to documents directory for sharing
    /// - Parameter tempURL: The temporary file URL
    /// - Returns: The new URL in the documents directory
    func moveToDocumentsForSharing(tempURL: URL) -> Result<URL, Error> {
        print("📹 DEBUG: Moving file to documents directory for sharing")

        do {
            // Get the documents directory
            let documentsDirectory = try FileManager.default.url(
                for: .documentDirectory,
                in: .userDomainMask,
                appropriateFor: nil,
                create: true
            )

            // Create a unique filename with the same extension
            let filename = "PoleVaultJump_\(UUID().uuidString).\(tempURL.pathExtension)"
            let destinationURL = documentsDirectory.appendingPathComponent(filename)

            // Remove existing file if it exists
            if FileManager.default.fileExists(atPath: destinationURL.path) {
                try FileManager.default.removeItem(at: destinationURL)
            }

            // Move the file
            try FileManager.default.moveItem(at: tempURL, to: destinationURL)

            print("📹 DEBUG: Successfully moved file to: \(destinationURL.path)")
            return .success(destinationURL)
        } catch {
            print("📹 ERROR: Failed to move file to documents directory: \(error.localizedDescription)")
            return .failure(ProcessorError.fileError("Failed to prepare file for sharing: \(error.localizedDescription)"))
        }
    }

    // MARK: - Public API

    /// Applies a simple watermark using ``WatermarkProcessor`` for the given asset.
    /// - Parameters:
    ///   - assetIdentifier: The PHAsset local identifier for the video.
    ///   - text: Watermark text. Defaults to the app name.
    ///   - completion: Completion handler with the URL to the processed video or an error.
    func processVideoWithWatermark(
        assetIdentifier: String,
        text: String = "PoleVaultLogPro",
        completion: @escaping (Result<URL, Error>) -> Void
    ) {
        cancelCurrentProcessing()

        Task { @MainActor in
            self.isProcessing = true
            self.currentProgress = 0.0
        }

        currentExportTask = Task {
            let authStatus = await withCheckedContinuation { cont in
                PHPhotoLibrary.requestAuthorization(for: .readWrite) { status in
                    cont.resume(returning: status)
                }
            }

            guard authStatus == .authorized else {
                await MainActor.run {
                    self.isProcessing = false
                    completion(.failure(ProcessorError.permissionDenied))
                }
                return
            }

            // Fetch asset
            let fetch = PHAsset.fetchAssets(withLocalIdentifiers: [assetIdentifier], options: nil)
            guard let asset = fetch.firstObject else {
                await MainActor.run {
                    self.isProcessing = false
                    completion(.failure(ProcessorError.assetNotFound))
                }
                return
            }

            self.applySimpleWatermark(to: asset, text: text) { result in
                Task { @MainActor in
                    self.isProcessing = false
                    self.currentProgress = 1.0
                    completion(result)
                }
            }
        }
    }

    /// Process a jump's video with overlays - main entry point
    /// - Parameters:
    ///   - jump: The jump to process
    ///   - options: Export options
    ///   - progressHandler: Optional handler for progress updates (0.0 to 1.0)
    ///   - completion: Completion handler with URL to processed video or error
    func processJump(
        _ jump: Jump,
        options: VideoExportOptions = .highQuality,
        progressHandler: ((Float) -> Void)? = nil,
        completion: @escaping (Result<URL, Error>) -> Void
    ) {
        // Find the first video media item for this jump
        guard let mediaItems = jump.mediaItems?.allObjects as? [JumpMedia] else {
            print("📹 ERROR: No media items found for jump")
            completion(.failure(ProcessorError.assetNotFound))
            return
        }

        guard let videoMedia = mediaItems.first(where: { $0.type == "video" }),
              let assetIdentifier = videoMedia.assetIdentifier else {
            print("📹 ERROR: No video found for jump")
            completion(.failure(ProcessorError.assetNotFound))
            return
        }

        processVideoWithOverlay(
            assetIdentifier: assetIdentifier,
            jump: jump,
            options: options,
            progressHandler: progressHandler,
            completion: completion
        )
    }

    /// Process a video with overlays using two-track composition
    /// - Parameters:
    ///   - assetIdentifier: The PHAsset identifier for the video
    ///   - jump: The jump associated with the video
    ///   - options: Export options
    ///   - progressHandler: Optional handler for progress updates (0.0 to 1.0)
    ///   - completion: Completion handler with URL to processed video or error
    func processVideoWithOverlay(
        assetIdentifier: String,
        jump: Jump,
        options: VideoExportOptions = .highQuality,
        progressHandler: ((Float) -> Void)? = nil,
        completion: @escaping (Result<URL, Error>) -> Void
    ) {
        print("📹 DEBUG: Starting video processing for asset: \(assetIdentifier)")
        print("📹 DEBUG: Export options - Quality: \(options.quality)")

        // Validate export options
        guard options.isValid else {
            print("📹 ERROR: Invalid export options")
            Task { @MainActor in
                completion(.failure(ProcessorError.invalidPreset))
            }
            return
        }

        // Cancel any existing processing
        cancelCurrentProcessing()

        // Update state
        Task { @MainActor in
            self.isProcessing = true
            self.currentProgress = 0.0
        }

        // Create a new export task for cancellation support
        currentExportTask = Task {
            // Check Photos permission first
            let authStatus = await withCheckedContinuation { continuation in
                PHPhotoLibrary.requestAuthorization(for: .readWrite) { status in
                    continuation.resume(returning: status)
                }
            }

            guard authStatus == .authorized else {
                print("📹 ERROR: Photos access not authorized")
                await MainActor.run {
                    self.isProcessing = false
                    completion(.failure(ProcessorError.permissionDenied))
                }
                return
            }

            print("📹 DEBUG: Photos authorization granted")

            // Fetch the asset from Photos
            let phAsset: PHAsset? = await withCheckedContinuation { continuation in
                DispatchQueue.global(qos: .userInitiated).async {
                    // First try to find the asset as a shared asset
                    var asset = SharedMediaManager.shared.fetchSharedAsset(with: assetIdentifier)

                    // If not found as a shared asset, try as a local asset
                    if asset == nil {
                        let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [assetIdentifier], options: nil)
                        asset = fetchResult.firstObject
                    }

                    continuation.resume(returning: asset)
                }
            }

            guard let phAsset = phAsset, phAsset.mediaType == .video else {
                print("📹 ERROR: Asset not found or not a video: \(assetIdentifier)")
                await MainActor.run {
                    self.isProcessing = false
                    completion(.failure(ProcessorError.assetNotFound))
                }
                return
            }

            print("📹 DEBUG: Video asset found - Duration: \(phAsset.duration)s")

            // Check for cancellation
            if Task.isCancelled {
                await MainActor.run {
                    self.isProcessing = false
                    completion(.failure(ProcessorError.cancelled))
                }
                return
            }

            // Get the source video URL
            do {
                let sourceURL = try await self.getVideoURL(from: phAsset)

                // Create overlay text from jump data
                let overlayText = self.createOverlayText(from: jump, options: options)

                // Create destination URL
                let destURL = URL(fileURLWithPath: NSTemporaryDirectory())
                    .appendingPathComponent("PoleVaultJump_\(UUID().uuidString)")
                    .appendingPathExtension("mp4")

                // Export video with overlay using two-track composition
                try await self.exportVideoWithTwoTrackOverlay(
                    sourceURL: sourceURL,
                    overlayText: overlayText,
                    destURL: destURL,
                    options: options,
                    progressHandler: progressHandler
                )

                await MainActor.run {
                    self.isProcessing = false
                    self.currentProgress = 1.0
                    print("📹 DEBUG: Video processing completed successfully")
                    completion(.success(destURL))
                }

            } catch {
                await MainActor.run {
                    self.isProcessing = false
                    print("📹 ERROR: Video processing failed: \(error.localizedDescription)")
                    completion(.failure(ProcessorError.exportFailed(error)))
                }
            }
        }
    }

    // MARK: - Private Implementation

    /// Apply a simple watermark to a video asset
    /// - Parameters:
    ///   - asset: The PHAsset video to watermark
    ///   - text: The watermark text
    ///   - completion: Completion handler with the URL to the processed video or an error
    private func applySimpleWatermark(
        to asset: PHAsset,
        text: String,
        completion: @escaping (Result<URL, Error>) -> Void
    ) {
        let options = PHVideoRequestOptions()
        options.isNetworkAccessAllowed = true
        options.version = .current
        options.deliveryMode = .highQualityFormat

        PHImageManager.default().requestAVAsset(forVideo: asset, options: options) { avAsset, _, info in
            if let error = info?[PHImageErrorKey] as? Error {
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
                return
            }

            guard let urlAsset = avAsset as? AVURLAsset else {
                DispatchQueue.main.async {
                    completion(.failure(ProcessorError.assetNotFound))
                }
                return
            }

            let outputURL = FileManager.default.temporaryDirectory
                .appendingPathComponent(UUID().uuidString)
                .appendingPathExtension("mp4")

            do {
                try self.processSimpleWatermark(
                    url: urlAsset.url,
                    text: text,
                    outputURL: outputURL,
                    completion: completion
                )
            } catch {
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
            }
        }
    }

    /// Process a video URL with a simple watermark
    private func processSimpleWatermark(
        url: URL,
        text: String,
        outputURL: URL,
        completion: @escaping (Result<URL, Error>) -> Void
    ) throws {
        let asset = AVURLAsset(url: url)

        guard let videoTrack = asset.tracks(withMediaType: .video).first else {
            throw ProcessorError.assetNotFound
        }

        let composition = AVMutableComposition()
        let compVideo = composition.addMutableTrack(
            withMediaType: .video,
            preferredTrackID: kCMPersistentTrackID_Invalid
        )!
        try compVideo.insertTimeRange(
            CMTimeRange(start: .zero, duration: asset.duration),
            of: videoTrack,
            at: .zero
        )

        // Add audio track if available
        if let audioTrack = asset.tracks(withMediaType: .audio).first {
            let compAudio = composition.addMutableTrack(
                withMediaType: .audio,
                preferredTrackID: kCMPersistentTrackID_Invalid
            )!
            try compAudio.insertTimeRange(
                CMTimeRange(start: .zero, duration: asset.duration),
                of: audioTrack,
                at: .zero
            )
        }

        let instruction = AVMutableVideoCompositionInstruction()
        instruction.timeRange = CMTimeRange(start: .zero, duration: asset.duration)

        let layerInstruction = AVMutableVideoCompositionLayerInstruction(assetTrack: compVideo)
        layerInstruction.setTransform(videoTrack.preferredTransform, at: .zero)
        instruction.layerInstructions = [layerInstruction]

        let videoComposition = AVMutableVideoComposition()
        videoComposition.instructions = [instruction]

        let frameRate = videoTrack.nominalFrameRate
        let timescale = frameRate > 0 ? Int32(frameRate) : 30
        videoComposition.frameDuration = CMTime(value: 1, timescale: timescale)

        // Determine render size accounting for orientation
        let naturalSize = videoTrack.naturalSize
        let isPortrait = abs(videoTrack.preferredTransform.b) == 1 && abs(videoTrack.preferredTransform.c) == 1
        videoComposition.renderSize = isPortrait ? CGSize(width: naturalSize.height, height: naturalSize.width) : naturalSize

        // Create watermark overlay layers
        let videoLayer = CALayer()
        videoLayer.frame = CGRect(origin: .zero, size: videoComposition.renderSize)

        // Styled container similar to the media viewer overlay
        let containerLayer = CALayer()
        containerLayer.backgroundColor = UIColor.black.withAlphaComponent(0.6).cgColor
        containerLayer.cornerRadius = 6
        containerLayer.masksToBounds = true
        containerLayer.frame = CGRect(
            x: videoComposition.renderSize.width - 200,
            y: 20,
            width: 180,
            height: 40
        )

        let overlayLayer = CATextLayer()
        overlayLayer.string = text
        overlayLayer.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        overlayLayer.fontSize = 14
        overlayLayer.alignmentMode = .left
        overlayLayer.foregroundColor = UIColor.white.withAlphaComponent(0.9).cgColor
        overlayLayer.contentsScale = UIScreen.main.scale
        overlayLayer.frame = CGRect(x: 12, y: 10, width: 156, height: 20)
        containerLayer.addSublayer(overlayLayer)

        let parentLayer = CALayer()
        parentLayer.frame = CGRect(origin: .zero, size: videoComposition.renderSize)
        parentLayer.addSublayer(videoLayer)
        parentLayer.addSublayer(containerLayer)

        videoComposition.animationTool = AVVideoCompositionCoreAnimationTool(
            postProcessingAsVideoLayer: videoLayer,
            in: parentLayer
        )

        // Use simulator-safe preset
        #if targetEnvironment(simulator)
        let presetName = AVAssetExportPreset640x480
        #else
        let presetName = AVAssetExportPresetHighestQuality
        #endif

        guard let exporter = AVAssetExportSession(asset: composition, presetName: presetName) else {
            throw ProcessorError.unableToCreateExportSession
        }
        exporter.outputURL = outputURL
        exporter.outputFileType = .mp4
        exporter.videoComposition = videoComposition

        exporter.exportAsynchronously {
            DispatchQueue.main.async {
                switch exporter.status {
                case .completed:
                    completion(.success(outputURL))
                default:
                    completion(.failure(exporter.error ?? ProcessorError.exportFailed(NSError(domain: "VideoProcessor", code: -1))))
                }
            }
        }
    }

    /// Get video URL from PHAsset
    private func getVideoURL(from phAsset: PHAsset) async throws -> URL {
        return try await withCheckedThrowingContinuation { continuation in
            let options = PHVideoRequestOptions()
            options.version = .current
            options.deliveryMode = .highQualityFormat
            options.isNetworkAccessAllowed = true

            PHImageManager.default().requestAVAsset(forVideo: phAsset, options: options) { avAsset, _, info in
                if let error = info?[PHImageErrorKey] as? Error {
                    continuation.resume(throwing: error)
                } else if let urlAsset = avAsset as? AVURLAsset {
                    continuation.resume(returning: urlAsset.url)
                } else {
                    continuation.resume(throwing: ProcessorError.assetNotFound)
                }
            }
        }
    }

    /// Create overlay text from jump data
    private func createOverlayText(from jump: Jump, options: VideoExportOptions) -> String {
        var textLines: [String] = []

        if options.includeJumpStats {
            let sessionTitle = jump.session?.title ?? "Untitled Session"
            let heightString = jump.barHeightCm > 0 ? String(format: "%.2f m", jump.barHeightCm / 100.0) : "Unknown Height"
            let attemptString = "Attempt \(jump.attemptIndex)"
            let resultString = jump.result?.capitalized ?? "Unknown"
            let barBungeeString = jump.useBar ? "Bar" : "Bungee"
            let poleName = jump.pole?.name ?? "No Pole"
            let runStart = jump.runStartCm > 0 ? String(format: "%.1f", jump.runStartCm / 100.0) : "0.0"
            let handHold = jump.handHoldCm > 0 ? String(format: "%.1f", jump.handHoldCm / 100.0) : "0.0"
            let takeOffStep = jump.takeOffStepCm > 0 ? String(format: "%.1f", jump.takeOffStepCm / 100.0) : "0.0"
            let standard = jump.standardCm > 0 ? String(format: "%.1f", jump.standardCm / 100.0) : "0.0"

            textLines.append(sessionTitle)
            textLines.append(heightString)
            textLines.append("\(attemptString) - \(resultString)")
            textLines.append(barBungeeString)
            textLines.append(poleName)
            textLines.append(runStart)
            textLines.append(handHold)
            textLines.append(takeOffStep)
            textLines.append(standard)
        }

        if options.includeSessionInfo {
            let sessionDate = jump.session?.date != nil ? Date(timeIntervalSinceReferenceDate: jump.session!.date) : Date()
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            formatter.timeStyle = .short
            let dateString = formatter.string(from: sessionDate)
            textLines.append(dateString)
        }

        if options.includeAppWatermark {
            textLines.append("PoleVaultLogPro")
        }

        return textLines.joined(separator: "\n")
    }

    /// Export video with two-track overlay composition
    private func exportVideoWithTwoTrackOverlay(
        sourceURL: URL,
        overlayText: String,
        destURL: URL,
        options: VideoExportOptions,
        progressHandler: ((Float) -> Void)? = nil
    ) async throws {
        // 1️⃣ Load original asset
        let sourceAsset = AVURLAsset(url: sourceURL)

        // Load asset properties
        let duration = try await sourceAsset.load(.duration)
        let videoTracks = try await sourceAsset.loadTracks(withMediaType: .video)
        guard let firstVideoTrack = videoTracks.first else {
            throw ProcessorError.assetNotFound
        }
        let naturalSize = try await firstVideoTrack.load(.naturalSize)
        let preferredTransform = try await firstVideoTrack.load(.preferredTransform)
        let nominalFrameRate = try await firstVideoTrack.load(.nominalFrameRate)

        // Determine final render size based on orientation
        let videoAngle = atan2(preferredTransform.b, preferredTransform.a)
        let isPortrait = abs(videoAngle) == .pi/2 || abs(videoAngle) == 3 * .pi/2
        let renderSize = isPortrait ?
            CGSize(width: naturalSize.height, height: naturalSize.width) :
            naturalSize

        // 2️⃣ Build an overlay clip spanning the entire video
        print("📹 DEBUG: Creating overlay with text: \(overlayText)")
        print("📹 DEBUG: Video size: \(naturalSize) -> render: \(renderSize)")

        let overlayURL = try await OverlayClipBuilder.buildOverlayClip(
            text: overlayText,
            duration: duration,
            videoSize: renderSize,
            frameRate: nominalFrameRate,
            font: .boldSystemFont(ofSize: 48),
            origin: CGPoint(x: 40, y: 40)
        )

        print("📹 DEBUG: Overlay clip created at: \(overlayURL.path)")

        // 3️⃣ Compose source + overlay tracks
        let compPair = try await CompositionBuilder.buildComposition(
            source: sourceAsset,
            overlayURL: overlayURL
        )

        // 4️⃣ Export the composition
        try await Exporter.export(
            composition: compPair.composition,
            videoComp: compPair.videoComposition,
            destinationURL: destURL,
            presetName: options.quality.avPreset,
            progressHandler: progressHandler
        )
    }
}

// MARK: - Overlay-clip builder (PNG → multi-frame .mov)
enum OverlayClipBuilder {

    /// Renders `text` into a transparent UIImage, then writes a multi-frame H.264/HEVC `.mov` that spans the entire video duration.
    static func buildOverlayClip(
        text: String,
        duration: CMTime,
        videoSize: CGSize,
        frameRate: Float,
        font: UIFont,
        origin: CGPoint
    ) async throws -> URL {

        // ▶︎ 1. Draw text into a transparent image
        let img = renderTextImage(
            text: text,
            canvasSize: videoSize,
            font: font,
            origin: origin
        )

        // ▶︎ 2. Prepare an AVAssetWriter
        let url = FileManager.default.temporaryDirectory
            .appendingPathComponent(UUID().uuidString)
            .appendingPathExtension("mov")

        let writer = try AVAssetWriter(url: url, fileType: .mov)

        // Use simulator-safe settings but maintain aspect ratio
        #if targetEnvironment(simulator)
        // Scale down while maintaining aspect ratio
        let maxDimension: CGFloat = 480
        let scale = min(maxDimension / videoSize.width, maxDimension / videoSize.height)
        let scaledWidth = Int(videoSize.width * scale)
        let scaledHeight = Int(videoSize.height * scale)

        let settings: [String: Any] = [
            AVVideoCodecKey: AVVideoCodecType.h264,
            AVVideoWidthKey: scaledWidth,
            AVVideoHeightKey: scaledHeight,
            AVVideoCompressionPropertiesKey: [
                AVVideoAverageBitRateKey: 1_000_000,  // Low bitrate
                AVVideoProfileLevelKey: AVVideoProfileLevelH264BaselineAutoLevel
            ]
        ]
        #else
        // Use a codec that preserves alpha so the overlay can be composited
        let codec: AVVideoCodecType
        if #available(iOS 13.0, *) {
            codec = .hevcWithAlpha
        } else {
            codec = .h264
        }

        let settings: [String: Any] = [
            AVVideoCodecKey: codec,
            AVVideoWidthKey: Int(videoSize.width),
            AVVideoHeightKey: Int(videoSize.height)
        ]
        #endif
        let input = AVAssetWriterInput(mediaType: .video, outputSettings: settings)
        input.expectsMediaDataInRealTime = false

        #if targetEnvironment(simulator)
        let adaptor = AVAssetWriterInputPixelBufferAdaptor(
            assetWriterInput: input,
            sourcePixelBufferAttributes: [
                kCVPixelBufferPixelFormatTypeKey as String: kCVPixelFormatType_32BGRA,
                kCVPixelBufferWidthKey as String: scaledWidth,
                kCVPixelBufferHeightKey as String: scaledHeight
            ]
        )
        #else
        let adaptor = AVAssetWriterInputPixelBufferAdaptor(
            assetWriterInput: input,
            sourcePixelBufferAttributes: [
                kCVPixelBufferPixelFormatTypeKey as String: kCVPixelFormatType_32BGRA,
                kCVPixelBufferWidthKey as String: Int(videoSize.width),
                kCVPixelBufferHeightKey as String: Int(videoSize.height)
            ]
        )
        #endif

        writer.add(input)
        writer.startWriting()
        writer.startSession(atSourceTime: .zero)

        // ▶︎ 3. Push pixel buffers for the entire duration so the overlay
        //     remains visible for the whole video
        guard let pxbuf = adaptor.pixelBufferPool.flatMap(makePixelBuffer(in:)) else {
            throw NSError(domain: "OverlayClipBuilder", code: -1,
                          userInfo: [NSLocalizedDescriptionKey: "Pixel-buffer pool unavailable"])
        }
        drawImage(img, into: pxbuf)

        let rate = frameRate > 0 ? frameRate : 30
        let frameDuration = CMTime(value: 1, timescale: CMTimeScale(rate))
        let frames = max(1, Int(CMTimeGetSeconds(duration) * Double(rate)))
        var presentationTime = CMTime.zero
        for _ in 0..<frames {
            while !input.isReadyForMoreMediaData { await Task.yield() }
            adaptor.append(pxbuf, withPresentationTime: presentationTime)
            presentationTime = presentationTime + frameDuration
        }

        input.markAsFinished()
        writer.endSession(atSourceTime: duration)
        await writer.finishWriting()

        return url
    }

    // MARK: helpers
    private static func renderTextImage(
        text: String,
        canvasSize: CGSize,
        font: UIFont,
        origin: CGPoint
    ) -> UIImage {
        UIGraphicsBeginImageContextWithOptions(canvasSize, false, 1.0)
        defer { UIGraphicsEndImageContext() }

        guard let context = UIGraphicsGetCurrentContext() else {
            return UIImage()
        }

        // Clear the context with transparent background
        context.clear(CGRect(origin: .zero, size: canvasSize))

        // Create overlay using the same card style as the media player
        let overlayData = parseOverlayText(text)
        renderStatsOverlay(context: context, canvasSize: canvasSize, overlayData: overlayData)

        return UIGraphicsGetImageFromCurrentImageContext() ?? UIImage()
    }

    private static func parseOverlayText(_ text: String) -> OverlayData {
        let lines = text.components(separatedBy: "\n")

        var sessionTitle = ""
        var height = ""
        var attemptResult = ""
        var sessionInfo = ""
        var watermark = ""
        var useBar = true
        var poleName = ""
        var attemptNumber = ""
        var runStart = ""
        var handHold = ""
        var takeOffStep = ""
        var standard = ""

        for (index, line) in lines.enumerated() {
            if line.contains("Attempt") {
                attemptResult = line
                // Extract attempt number
                let components = line.components(separatedBy: " ")
                if components.count >= 2 {
                    attemptNumber = components[1]
                }
            } else if line.contains("m") || line.contains("ft") {
                height = line
            } else if line.contains("PoleVaultLogPro") {
                watermark = line
            } else if line.contains(":") || line.contains("AM") || line.contains("PM") {
                sessionInfo = line
            } else if line.lowercased() == "bar" {
                useBar = true
            } else if line.lowercased() == "bungee" {
                useBar = false
            } else if index == 4 { // Pole name is 5th line
                poleName = line
            } else if index == 5 { // Run start is 6th line
                runStart = line
            } else if index == 6 { // Hand hold is 7th line
                handHold = line
            } else if index == 7 { // Take off step is 8th line
                takeOffStep = line
            } else if index == 8 { // Standard is 9th line
                standard = line
            } else if !line.isEmpty && sessionTitle.isEmpty {
                sessionTitle = line
            }
        }

        return OverlayData(
            sessionTitle: sessionTitle,
            height: height,
            attemptResult: attemptResult,
            sessionInfo: sessionInfo,
            watermark: watermark,
            useBar: useBar,
            poleName: poleName,
            attemptNumber: attemptNumber,
            runStart: runStart,
            handHold: handHold,
            takeOffStep: takeOffStep,
            standard: standard
        )
    }

    private static func renderStyledOverlay(context: CGContext, canvasSize: CGSize, overlayData: OverlayData) {
        print("📹 DEBUG: Rendering styled overlay on canvas: \(canvasSize)")

        // Top Left: Title of Season - Height
        renderTopLeftOverlay(context: context, canvasSize: canvasSize, overlayData: overlayData)

        // Top Right: Icon - AppName
        renderTopRightOverlay(context: context, canvasSize: canvasSize, overlayData: overlayData)

        // Bottom Left: Pass/Make/Miss | Bar/Bungee | PoleName | Attempt#
        renderBottomLeftOverlay(context: context, canvasSize: canvasSize, overlayData: overlayData)

        // Bottom Center: Date of Jump
        renderBottomCenterOverlay(context: context, canvasSize: canvasSize, overlayData: overlayData)

        // Bottom Right: Run | Grip | Step | Std
        renderBottomRightOverlay(context: context, canvasSize: canvasSize, overlayData: overlayData)
    }

    // MARK: - New Four-Section Overlay Rendering

    private static func renderTopLeftOverlay(context: CGContext, canvasSize: CGSize, overlayData: OverlayData) {
        // Scale all dimensions based on video size
        let scale = min(canvasSize.width, canvasSize.height) / 720.0 // Base scale on 720p
        let padding: CGFloat = 20 * scale
        let overlayWidth: CGFloat = min(300 * scale, canvasSize.width * 0.4)
        let overlayHeight: CGFloat = 60 * scale
        let cornerRadius: CGFloat = 8 * scale

        // Background
        let backgroundRect = CGRect(x: padding, y: padding, width: overlayWidth, height: overlayHeight)
        context.setFillColor(UIColor.black.withAlphaComponent(0.7).cgColor)
        let backgroundPath = UIBezierPath(roundedRect: backgroundRect, cornerRadius: cornerRadius)
        context.addPath(backgroundPath.cgPath)
        context.fillPath()

        // Session Title
        let titleFontSize: CGFloat = 16 * scale
        let titleAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.boldSystemFont(ofSize: titleFontSize),
            .foregroundColor: UIColor.white
        ]
        let titleText = NSAttributedString(string: overlayData.sessionTitle, attributes: titleAttributes)
        let titlePoint = CGPoint(x: padding + 12 * scale, y: padding + 8 * scale)
        titleText.draw(at: titlePoint)

        // Height
        let heightFontSize: CGFloat = 20 * scale
        let heightAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.boldSystemFont(ofSize: heightFontSize),
            .foregroundColor: UIColor.systemBlue
        ]
        let heightText = NSAttributedString(string: overlayData.height, attributes: heightAttributes)
        let heightPoint = CGPoint(x: padding + 12 * scale, y: padding + 32 * scale)
        heightText.draw(at: heightPoint)
    }

    private static func renderTopRightOverlay(context: CGContext, canvasSize: CGSize, overlayData: OverlayData) {
        let scale = min(canvasSize.width, canvasSize.height) / 720.0
        let padding: CGFloat = 20 * scale
        let overlayWidth: CGFloat = 180 * scale
        let overlayHeight: CGFloat = 40 * scale
        let cornerRadius: CGFloat = 6 * scale
        let xPosition = canvasSize.width - overlayWidth - padding

        // Background
        let backgroundRect = CGRect(x: xPosition, y: padding, width: overlayWidth, height: overlayHeight)
        context.setFillColor(UIColor.black.withAlphaComponent(0.6).cgColor)
        let backgroundPath = UIBezierPath(roundedRect: backgroundRect, cornerRadius: cornerRadius)
        context.addPath(backgroundPath.cgPath)
        context.fillPath()

        // App Name
        let appNameFontSize: CGFloat = 14 * scale
        let appNameAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: appNameFontSize, weight: .medium),
            .foregroundColor: UIColor.white.withAlphaComponent(0.9)
        ]
        let appNameText = NSAttributedString(string: "PoleVaultLogPro", attributes: appNameAttributes)
        let appNamePoint = CGPoint(x: xPosition + 12 * scale, y: padding + 12 * scale)
        appNameText.draw(at: appNamePoint)
    }

    private static func renderBottomLeftOverlay(context: CGContext, canvasSize: CGSize, overlayData: OverlayData) {
        let scale = min(canvasSize.width, canvasSize.height) / 720.0
        let padding: CGFloat = 20 * scale
        let overlayWidth: CGFloat = min(350 * scale, canvasSize.width * 0.45)
        let overlayHeight: CGFloat = 60 * scale
        let cornerRadius: CGFloat = 8 * scale
        let yPosition = canvasSize.height - overlayHeight - padding

        // Background
        let backgroundRect = CGRect(x: padding, y: yPosition, width: overlayWidth, height: overlayHeight)
        context.setFillColor(UIColor.black.withAlphaComponent(0.7).cgColor)
        let backgroundPath = UIBezierPath(roundedRect: backgroundRect, cornerRadius: cornerRadius)
        context.addPath(backgroundPath.cgPath)
        context.fillPath()

        // Extract result from attemptResult
        let result = overlayData.attemptResult.components(separatedBy: " - ").last ?? "Unknown"
        let barBungeeString = overlayData.useBar ? "Bar" : "Bungee"

        // Create the combined text: Pass/Make/Miss | Bar/Bungee | PoleName | Attempt#
        let combinedText = "\(result) | \(barBungeeString) | \(overlayData.poleName) | #\(overlayData.attemptNumber)"

        let textFontSize: CGFloat = 14 * scale
        let textAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: textFontSize, weight: .medium),
            .foregroundColor: UIColor.white
        ]
        let textString = NSAttributedString(string: combinedText, attributes: textAttributes)
        let textPoint = CGPoint(x: padding + 12 * scale, y: yPosition + 20 * scale)
        textString.draw(at: textPoint)
    }

    private static func renderBottomCenterOverlay(context: CGContext, canvasSize: CGSize, overlayData: OverlayData) {
        let scale = min(canvasSize.width, canvasSize.height) / 720.0
        let overlayWidth: CGFloat = 200 * scale
        let overlayHeight: CGFloat = 40 * scale
        let padding: CGFloat = 20 * scale
        let cornerRadius: CGFloat = 8 * scale
        let xPosition = (canvasSize.width - overlayWidth) / 2
        let yPosition = canvasSize.height - overlayHeight - padding

        // Background
        let backgroundRect = CGRect(x: xPosition, y: yPosition, width: overlayWidth, height: overlayHeight)
        context.setFillColor(UIColor.black.withAlphaComponent(0.7).cgColor)
        let backgroundPath = UIBezierPath(roundedRect: backgroundRect, cornerRadius: cornerRadius)
        context.addPath(backgroundPath.cgPath)
        context.fillPath()

        // Date
        let dateFontSize: CGFloat = 14 * scale
        let dateAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: dateFontSize, weight: .medium),
            .foregroundColor: UIColor.white
        ]
        let dateText = NSAttributedString(string: overlayData.sessionInfo, attributes: dateAttributes)
        let dateSize = dateText.size()
        let datePoint = CGPoint(
            x: xPosition + (overlayWidth - dateSize.width) / 2,
            y: yPosition + (overlayHeight - dateSize.height) / 2
        )
        dateText.draw(at: datePoint)
    }

    private static func renderBottomRightOverlay(context: CGContext, canvasSize: CGSize, overlayData: OverlayData) {
        let scale = min(canvasSize.width, canvasSize.height) / 720.0
        let padding: CGFloat = 20 * scale
        let overlayWidth: CGFloat = min(250 * scale, canvasSize.width * 0.35)
        let overlayHeight: CGFloat = 60 * scale
        let cornerRadius: CGFloat = 8 * scale
        let xPosition = canvasSize.width - overlayWidth - padding
        let yPosition = canvasSize.height - overlayHeight - padding

        // Background
        let backgroundRect = CGRect(x: xPosition, y: yPosition, width: overlayWidth, height: overlayHeight)
        context.setFillColor(UIColor.black.withAlphaComponent(0.7).cgColor)
        let backgroundPath = UIBezierPath(roundedRect: backgroundRect, cornerRadius: cornerRadius)
        context.addPath(backgroundPath.cgPath)
        context.fillPath()

        // Technical measurements: Run | Grip | Step | Std
        let techText = "Run: \(overlayData.runStart) | Grip: \(overlayData.handHold) | Step: \(overlayData.takeOffStep) | Std: \(overlayData.standard)"

        let textFontSize: CGFloat = 12 * scale
        let textAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: textFontSize, weight: .medium),
            .foregroundColor: UIColor.white
        ]
        let textString = NSAttributedString(string: techText, attributes: textAttributes)
        let textPoint = CGPoint(x: xPosition + 12 * scale, y: yPosition + 20 * scale)
        textString.draw(at: textPoint)
    }

    private static func renderStatsOverlay(context: CGContext, canvasSize: CGSize, overlayData: OverlayData) {
        let padding: CGFloat = 20
        let overlayWidth: CGFloat = min(400, canvasSize.width - 40)
        let overlayHeight: CGFloat = 120

        // Background with rounded corners and transparency
        let backgroundRect = CGRect(x: padding, y: padding, width: overlayWidth, height: overlayHeight)
        context.setFillColor(UIColor.black.withAlphaComponent(0.7).cgColor)
        let backgroundPath = UIBezierPath(roundedRect: backgroundRect, cornerRadius: 12)
        context.addPath(backgroundPath.cgPath)
        context.fillPath()

        // Session title
        var yOffset: CGFloat = padding + 15
        if !overlayData.sessionTitle.isEmpty {
            let titleFont = UIFont.boldSystemFont(ofSize: 18)
            let titleAttributes: [NSAttributedString.Key: Any] = [
                .font: titleFont,
                .foregroundColor: UIColor.white
            ]
            let titleRect = CGRect(x: padding + 15, y: yOffset, width: overlayWidth - 30, height: 25)
            (overlayData.sessionTitle as NSString).draw(in: titleRect, withAttributes: titleAttributes)
            yOffset += 30
        }

        // Height with accent color
        if !overlayData.height.isEmpty {
            let heightFont = UIFont.boldSystemFont(ofSize: 24)
            let heightAttributes: [NSAttributedString.Key: Any] = [
                .font: heightFont,
                .foregroundColor: UIColor.systemBlue  // Accent color
            ]
            let heightRect = CGRect(x: padding + 15, y: yOffset, width: overlayWidth - 30, height: 30)
            (overlayData.height as NSString).draw(in: heightRect, withAttributes: heightAttributes)
            yOffset += 35
        }

        // Attempt and result with styled background
        if !overlayData.attemptResult.isEmpty {
            let components = overlayData.attemptResult.components(separatedBy: " - ")
            let attempt = components.first ?? ""
            let result = components.count > 1 ? components[1] : ""

            var xOffset: CGFloat = padding + 15

            // Attempt badge
            if !attempt.isEmpty {
                let attemptFont = UIFont.systemFont(ofSize: 12, weight: .medium)
                let attemptSize = (attempt as NSString).size(withAttributes: [.font: attemptFont])
                let attemptBadgeRect = CGRect(x: xOffset, y: yOffset, width: attemptSize.width + 12, height: 20)

                context.setFillColor(UIColor.gray.withAlphaComponent(0.6).cgColor)
                let attemptBadgePath = UIBezierPath(roundedRect: attemptBadgeRect, cornerRadius: 4)
                context.addPath(attemptBadgePath.cgPath)
                context.fillPath()

                let attemptAttributes: [NSAttributedString.Key: Any] = [
                    .font: attemptFont,
                    .foregroundColor: UIColor.white
                ]
                let attemptTextRect = CGRect(x: xOffset + 6, y: yOffset + 2, width: attemptSize.width, height: 16)
                (attempt as NSString).draw(in: attemptTextRect, withAttributes: attemptAttributes)

                xOffset += attemptBadgeRect.width + 10
            }

            // Result badge with color coding
            if !result.isEmpty {
                let resultFont = UIFont.systemFont(ofSize: 12, weight: .medium)
                let resultSize = (result as NSString).size(withAttributes: [.font: resultFont])
                let resultBadgeRect = CGRect(x: xOffset, y: yOffset, width: resultSize.width + 12, height: 20)

                let resultColor: UIColor
                switch result.lowercased() {
                case "made", "make":
                    resultColor = UIColor.systemGreen
                case "miss":
                    resultColor = UIColor.systemRed
                case "pass":
                    resultColor = UIColor.systemOrange
                default:
                    resultColor = UIColor.gray
                }

                context.setFillColor(resultColor.withAlphaComponent(0.6).cgColor)
                let resultBadgePath = UIBezierPath(roundedRect: resultBadgeRect, cornerRadius: 4)
                context.addPath(resultBadgePath.cgPath)
                context.fillPath()

                let resultAttributes: [NSAttributedString.Key: Any] = [
                    .font: resultFont,
                    .foregroundColor: UIColor.white
                ]
                let resultTextRect = CGRect(x: xOffset + 6, y: yOffset + 2, width: resultSize.width, height: 16)
                (result as NSString).draw(in: resultTextRect, withAttributes: resultAttributes)
            }
        }
    }

    private static func renderSessionInfoOverlay(context: CGContext, canvasSize: CGSize, sessionInfo: String) {
        let padding: CGFloat = 20
        let overlayWidth: CGFloat = min(300, canvasSize.width - 40)
        let overlayHeight: CGFloat = 40
        let yPosition = canvasSize.height - overlayHeight - padding

        // Background
        let backgroundRect = CGRect(x: padding, y: yPosition, width: overlayWidth, height: overlayHeight)
        context.setFillColor(UIColor.black.withAlphaComponent(0.7).cgColor)
        let backgroundPath = UIBezierPath(roundedRect: backgroundRect, cornerRadius: 8)
        context.addPath(backgroundPath.cgPath)
        context.fillPath()

        // Session info text
        let infoFont = UIFont.systemFont(ofSize: 14)
        let infoAttributes: [NSAttributedString.Key: Any] = [
            .font: infoFont,
            .foregroundColor: UIColor.white
        ]
        let textRect = CGRect(x: padding + 10, y: yPosition + 10, width: overlayWidth - 20, height: 20)
        (sessionInfo as NSString).draw(in: textRect, withAttributes: infoAttributes)
    }

    private static func renderWatermarkOverlay(context: CGContext, canvasSize: CGSize, watermark: String) {
        let padding: CGFloat = 20
        let overlayWidth: CGFloat = 180
        let overlayHeight: CGFloat = 30
        let xPosition = canvasSize.width - overlayWidth - padding

        // Background
        let backgroundRect = CGRect(x: xPosition, y: padding, width: overlayWidth, height: overlayHeight)
        context.setFillColor(UIColor.black.withAlphaComponent(0.5).cgColor)
        let backgroundPath = UIBezierPath(roundedRect: backgroundRect, cornerRadius: 6)
        context.addPath(backgroundPath.cgPath)
        context.fillPath()

        // Watermark text
        let watermarkFont = UIFont.systemFont(ofSize: 12, weight: .medium)
        let watermarkAttributes: [NSAttributedString.Key: Any] = [
            .font: watermarkFont,
            .foregroundColor: UIColor.white.withAlphaComponent(0.8)
        ]
        let textRect = CGRect(x: xPosition + 8, y: padding + 8, width: overlayWidth - 16, height: 14)
        (watermark as NSString).draw(in: textRect, withAttributes: watermarkAttributes)
    }

    private struct OverlayData {
        let sessionTitle: String
        let height: String
        let attemptResult: String
        let sessionInfo: String
        let watermark: String
        let useBar: Bool
        let poleName: String
        let attemptNumber: String
        let runStart: String
        let handHold: String
        let takeOffStep: String
        let standard: String
    }

    private static func makePixelBuffer(in pool: CVPixelBufferPool) -> CVPixelBuffer? {
        var buf: CVPixelBuffer?
        CVPixelBufferPoolCreatePixelBuffer(nil, pool, &buf)
        return buf
    }

    private static func drawImage(_ image: UIImage, into buffer: CVPixelBuffer) {
        CVPixelBufferLockBaseAddress(buffer, [])
        defer { CVPixelBufferUnlockBaseAddress(buffer, []) }

        let context = CGContext(
            data: CVPixelBufferGetBaseAddress(buffer),
            width: CVPixelBufferGetWidth(buffer),
            height: CVPixelBufferGetHeight(buffer),
            bitsPerComponent: 8,
            bytesPerRow: CVPixelBufferGetBytesPerRow(buffer),
            space: CGColorSpaceCreateDeviceRGB(),
            bitmapInfo: CGImageAlphaInfo.premultipliedFirst.rawValue
        )!
        context.clear(CGRect(origin: .zero,
                             size: CGSize(width: CVPixelBufferGetWidth(buffer),
                                          height: CVPixelBufferGetHeight(buffer))))
        context.draw(image.cgImage!, in: CGRect(origin: .zero, size: image.size))
    }
}

// MARK: - Composition builder (source + overlay tracks)
enum CompositionBuilder {

    typealias CompositionPair = (composition: AVMutableComposition,
                                 videoComposition: AVMutableVideoComposition)

    static func buildComposition(
        source: AVAsset,
        overlayURL: URL
    ) async throws -> CompositionPair {

        let overlayAsset = AVURLAsset(url: overlayURL)
        let comp = AVMutableComposition()

        // Load tracks using async APIs
        let srcVideoTracks = try await source.loadTracks(withMediaType: .video)
        let ovlVideoTracks = try await overlayAsset.loadTracks(withMediaType: .video)

        guard let srcVid = srcVideoTracks.first,
              let ovlVid = ovlVideoTracks.first else {
            throw NSError(domain: "CompositionBuilder", code: -2,
                         userInfo: [NSLocalizedDescriptionKey: "Missing video tracks"])
        }

        let srcVidTrack = comp.addMutableTrack(
            withMediaType: .video, preferredTrackID: kCMPersistentTrackID_Invalid)!
        let ovlVidTrack = comp.addMutableTrack(
            withMediaType: .video, preferredTrackID: kCMPersistentTrackID_Invalid)!

        let duration = try await source.load(.duration)
        let timeRange = CMTimeRange(start: .zero, duration: duration)

        try srcVidTrack.insertTimeRange(timeRange, of: srcVid, at: .zero)
        try ovlVidTrack.insertTimeRange(timeRange, of: ovlVid, at: .zero)

        // Add audio track if available
        let srcAudioTracks = try await source.loadTracks(withMediaType: .audio)
        if let srcAud = srcAudioTracks.first {
            let srcAudTrack = comp.addMutableTrack(
                withMediaType: .audio, preferredTrackID: kCMPersistentTrackID_Invalid)!
            try srcAudTrack.insertTimeRange(timeRange, of: srcAud, at: .zero)
        }

        // Layer stack: overlay on top of source
        let instruction = AVMutableVideoCompositionInstruction()
        instruction.timeRange = timeRange

        let srcInstruction = AVMutableVideoCompositionLayerInstruction(assetTrack: srcVidTrack)
        let overlayInstruction = AVMutableVideoCompositionLayerInstruction(assetTrack: ovlVidTrack)
        overlayInstruction.setOpacity(1.0, at: .zero)

        instruction.layerInstructions = [srcInstruction, overlayInstruction]

        let vcomp = AVMutableVideoComposition()
        vcomp.instructions = [instruction]

        let minFrameDuration = try await srcVid.load(.minFrameDuration)
        let naturalSize = try await srcVid.load(.naturalSize)
        let preferredTransform = try await srcVid.load(.preferredTransform)

        vcomp.frameDuration = minFrameDuration

        // Apply proper transform to maintain orientation
        let videoAngle = atan2(preferredTransform.b, preferredTransform.a)
        let isPortrait = abs(videoAngle) == .pi/2 || abs(videoAngle) == 3 * .pi/2

        if isPortrait {
            // For portrait videos, swap width and height
            vcomp.renderSize = CGSize(width: naturalSize.height, height: naturalSize.width)
        } else {
            vcomp.renderSize = naturalSize
        }

        // Apply transform to source video layer instruction
        srcInstruction.setTransform(preferredTransform, at: .zero)

        print("📹 DEBUG: Video transform applied - angle: \(videoAngle), isPortrait: \(isPortrait)")
        print("📹 DEBUG: Natural size: \(naturalSize), Render size: \(vcomp.renderSize)")

        return (comp, vcomp)
    }
}

// MARK: - Exporter
enum Exporter {

    static func export(
        composition: AVMutableComposition,
        videoComp: AVMutableVideoComposition,
        destinationURL: URL,
        presetName: String = AVAssetExportPresetHighestQuality,
        progressHandler: ((Float) -> Void)? = nil
    ) async throws {

        try? FileManager.default.removeItem(at: destinationURL)

        // Use simulator-safe preset
        #if targetEnvironment(simulator)
        let safePresetName = AVAssetExportPreset640x480
        #else
        let safePresetName = presetName
        #endif

        guard let sess = AVAssetExportSession(asset: composition,
                                              presetName: safePresetName) else {
            throw NSError(domain: "Exporter", code: -3,
                          userInfo: [NSLocalizedDescriptionKey: "Export session unavailable"])
        }
        sess.outputFileType = .mp4
        sess.outputURL = destinationURL
        sess.videoComposition = videoComp

        // Set up progress observation if handler provided
        var progressObservation: NSKeyValueObservation?
        if let progressHandler = progressHandler {
            progressObservation = sess.observe(\.progress, options: [.new]) { session, change in
                if let newProgress = change.newValue {
                    Task { @MainActor in
                        progressHandler(newProgress)
                    }
                }
            }
        }

        defer {
            progressObservation?.invalidate()
        }

        // Use modern async export API if available, fallback to legacy
        if #available(iOS 18.0, *) {
            try await sess.export(to: destinationURL, as: .mp4)
        } else {
            try await withCheckedThrowingContinuation { cont in
                sess.exportAsynchronously {
                    switch sess.status {
                    case .completed:
                        cont.resume()
                    case .failed,
                         .cancelled:
                        cont.resume(throwing: sess.error ?? NSError(domain: "Exporter", code: -4,
                                                                   userInfo: [NSLocalizedDescriptionKey: "Export failed"]))
                    default:
                        break
                    }
                }
            }
        }
    }

    // MARK: - Legacy API Support

    /// Legacy method for backward compatibility
    /// Builds a new video that contains the overlay and writes it to a shareable URL.
    /// - Parameters:
    ///   - assetIdentifier: Local-identifier from `PHAsset`.
    ///   - overlayImage: A `UIImage` that will be burned on top of every frame.
    ///   - completion: Called on the main queue with either the exported file-URL
    ///                 or an error.
    func processVideoWithOverlay(
        assetIdentifier: String,
        overlayImage: UIImage,
        completion: @escaping (Result<URL, Error>) -> Void
    ) {
        print("📹 DEBUG: Starting video processing with overlay image for asset: \(assetIdentifier)")

        // For now, this legacy method will use simple text overlay
        // In the future, we could extend the two-track system to support image overlays
        Task { @MainActor in
            completion(.failure(VideoProcessor.ProcessorError.invalidPreset))
        }
    }
}

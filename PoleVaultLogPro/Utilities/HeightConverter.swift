import Foundation
import SwiftUI

/// Utility for converting between centimeters and feet/inches
struct HeightConverter {
    /// Converts centimeters to feet and decimal inches
    /// - Parameter cm: Height in centimeters
    /// - Returns: Formatted string like "13' 3.5""
    static func cmToFeetDecimalInchesString(_ cm: Double) -> String {
        let inches = cm / 2.54
        let feet = Int(inches / 12)
        let remainingInches = inches.truncatingRemainder(dividingBy: 12)

        // Format with one decimal place
        return String(format: "%d' %.1f\"", feet, remainingInches)
    }
    /// Converts centimeters to a formatted feet and inches string with fractions
    /// - Parameter cm: Height in centimeters
    /// - Returns: Formatted string like "13' 5 1/4""
    static func cmToFeetInchesString(_ cm: Double) -> String {
        let inches = cm / 2.54
        var feet = Int(inches / 12)
        let remainingInches = inches.truncatingRemainder(dividingBy: 12)

        // Round to the nearest quarter inch (0.25)
        var quarterInches = Int(round(remainingInches * 4)) // Multiply by 4 to get quarter inches

        // If rounding pushed us to a full extra foot, adjust accordingly
        if quarterInches == 48 { // 12 inches * 4
            feet += 1
            quarterInches = 0
        }

        let wholeInches = quarterInches / 4
        let fractionNumerator = quarterInches % 4

        let inchesFormatted: String

        if fractionNumerator == 0 {
            // Whole inch
            inchesFormatted = "\(wholeInches)"
        } else if fractionNumerator == 1 {
            // Quarter inch
            inchesFormatted = "\(wholeInches) 1/4"
        } else if fractionNumerator == 2 {
            // Half inch
            inchesFormatted = "\(wholeInches) 1/2"
        } else {
            // Three-quarter inch
            inchesFormatted = "\(wholeInches) 3/4"
        }

        return "\(feet)' \(inchesFormatted)\""
    }

    /// Converts centimeters to a formatted meters string
    /// - Parameter cm: Height in centimeters
    /// - Returns: Formatted string like "2.55 m"
    static func cmToMetersString(_ cm: Double) -> String {
        let meters = cm / 100.0

        // Format with two decimal places
        return String(format: "%.2f m", meters)
    }

    /// Formats a height in centimeters as a string based on user preference
    /// - Parameters:
    ///   - cm: Height in centimeters
    ///   - useMetric: Whether to use metric as the primary unit
    ///   - showBoth: Whether to show both units
    /// - Returns: Formatted string with both units if requested
    static func formatHeightString(cm: Double, useMetric: Bool, showBoth: Bool = true) -> String {
        if showBoth {
            if useMetric {
                return "\(cmToMetersString(cm)) • \(cmToFeetInchesString(cm))"
            } else {
                return "\(cmToFeetInchesString(cm)) • \(cmToMetersString(cm))"
            }
        } else {
            return useMetric ? cmToMetersString(cm) : cmToFeetInchesString(cm)
        }
    }

    /// Formats a measurement in centimeters as a string based on user preference
    /// - Parameters:
    ///   - cm: Measurement in centimeters
    ///   - useMetric: Whether to use metric as the primary unit
    ///   - showBoth: Whether to show both units
    /// - Returns: Formatted string with both units if requested
    static func formatMeasurementString(cm: Double, useMetric: Bool, showBoth: Bool = true) -> String {
        // For metric, show as meters
        let metricString = cmToMetersString(cm)

        // For imperial, use feet and inches with fractions
        let imperialString = cmToFeetInchesString(cm)

        if showBoth {
            if useMetric {
                return "\(metricString) • \(imperialString)"
            } else {
                return "\(imperialString) • \(metricString)"
            }
        } else {
            return useMetric ? metricString : imperialString
        }
    }

    /// Formats a height value for display in the app (for heights like bar height)
    /// - Parameter cm: Height in centimeters
    /// - Returns: Formatted string with both units
    static func formatHeight(cm: Double) -> String {
        return formatHeightString(cm: cm, useMetric: AppTheme.useMetricSystem)
    }

    /// Returns a tuple of primary and secondary height strings for two-line display
    /// - Parameter cm: Height in centimeters
    /// - Returns: Tuple with (primaryUnit, secondaryUnit) based on user preference
    static func formatHeightTwoLine(cm: Double) -> (primary: String, secondary: String?) {
        if AppTheme.useMetricSystem {
            let secondary = AppTheme.showSecondaryUnits ? cmToFeetInchesString(cm) : nil
            return (cmToMetersString(cm), secondary)
        } else {
            let secondary = AppTheme.showSecondaryUnits ? cmToMetersString(cm) : nil
            return (cmToFeetInchesString(cm), secondary)
        }
    }

    /// Formats a measurement value for display in the app (for measurements like run start, hand hold, etc.)
    /// - Parameter cm: Measurement in centimeters
    /// - Returns: Formatted string with both units
    static func formatMeasurement(cm: Double) -> String {
        return formatMeasurementString(cm: cm, useMetric: AppTheme.useMetricSystem)
    }

    /// Formats a step measurement with whole feet and inches (no partial feet)
    /// - Parameter cm: Measurement in centimeters
    /// - Returns: Formatted string like "46' 0"" instead of "45' 12""
    static func formatStepMeasurement(cm: Double) -> String {
        let inches = cm / 2.54
        var feet = Int(inches / 12)
        var remainingInches = Int(round(inches.truncatingRemainder(dividingBy: 12)))

        // Handle case where inches is 12 (should be 1 foot, 0 inches)
        if remainingInches == 12 {
            feet += 1
            remainingInches = 0
        }

        return "\(feet)' \(remainingInches)\""
    }

    /// Formats a hand hold measurement with whole feet and inches (no fractions)
    /// - Parameter cm: Measurement in centimeters
    /// - Returns: Formatted string like "13' 5"" (no quarter-inch fractions)
    static func formatHandHoldMeasurement(cm: Double) -> String {
        let inches = cm / 2.54
        var feet = Int(inches / 12)
        var remainingInches = Int(round(inches.truncatingRemainder(dividingBy: 12)))

        // Handle case where inches is 12 (should be 1 foot, 0 inches)
        if remainingInches == 12 {
            feet += 1
            remainingInches = 0
        }

        return "\(feet)' \(remainingInches)\""
    }

    /// Rounds a centimeter value to the nearest whole-inch equivalent in centimeters
    /// - Parameter cm: Height in centimeters
    /// - Returns: Height in centimeters rounded to the nearest whole-inch equivalent
    static func roundToWholeInchInCm(_ cm: Double) -> Double {
        // Convert to inches
        let inches = cm / 2.54

        // Round to the nearest whole inch
        let wholeInches = round(inches)

        // Convert back to centimeters
        return wholeInches * 2.54
    }

    /// Converts centimeters to total inches
    /// - Parameter cm: Height in centimeters
    /// - Returns: Height in inches
    static func cmToInches(_ cm: Double) -> Double {
        return cm / 2.54
    }

    /// Converts inches to centimeters
    /// - Parameter inches: Height in inches
    /// - Returns: Height in centimeters
    static func inchesToCm(_ inches: Double) -> Double {
        return inches * 2.54
    }

    /// Rounds a centimeter value to the nearest quarter-inch equivalent in centimeters
    /// - Parameter cm: Height in centimeters
    /// - Returns: Height in centimeters rounded to the nearest quarter-inch equivalent
    static func roundToQuarterInchInCm(_ cm: Double) -> Double {
        // Convert to inches
        let inches = cm / 2.54

        // Round to the nearest quarter inch
        let quarterInches = round(inches * 4) / 4

        // Convert back to centimeters
        return quarterInches * 2.54
    }

    /// Converts feet and inches to centimeters
    /// - Parameters:
    ///   - feet: Number of feet
    ///   - inches: Number of inches
    /// - Returns: Height in centimeters
    static func feetAndInchesToCm(feet: Int, inches: Double) -> Double {
        let totalInches = Double(feet) * 12 + inches
        return totalInches * 2.54
    }

    /// Parses a feet/inches string into centimeters
    /// - Parameter feetInchesString: String in format like "13' 5 1/4"" or "13'5""
    /// - Returns: Height in centimeters, or nil if the string couldn't be parsed
    static func parseFeetInchesString(_ feetInchesString: String) -> Double? {
        // Remove any quotes
        let cleaned = feetInchesString.replacingOccurrences(of: "\"", with: "")

        // Split by the foot mark
        let components = cleaned.components(separatedBy: "'")
        guard components.count == 2 else { return nil }

        // Parse feet
        guard let feet = Int(components[0].trimmingCharacters(in: .whitespaces)) else { return nil }

        // Parse inches with potential fractions
        let inchesStr = components[1].trimmingCharacters(in: .whitespaces)

        // Check if there's a fraction
        if inchesStr.contains("/") {
            // Split by space to separate whole number from fraction
            let parts = inchesStr.components(separatedBy: .whitespaces)

            if parts.count == 1 {
                // Just a fraction like "1/4"
                if let fractionValue = parseFraction(parts[0]) {
                    return feetAndInchesToCm(feet: feet, inches: fractionValue)
                }
            } else if parts.count == 2 {
                // Whole number and fraction like "5 1/4"
                guard let wholeInches = Double(parts[0]) else { return nil }
                if let fractionValue = parseFraction(parts[1]) {
                    return feetAndInchesToCm(feet: feet, inches: wholeInches + fractionValue)
                }
            }
            return nil
        } else {
            // Just a whole number
            guard let inches = Double(inchesStr) else { return nil }
            return feetAndInchesToCm(feet: feet, inches: inches)
        }
    }

    /// Helper to parse a fraction string like "1/4" into a decimal value
    private static func parseFraction(_ fractionStr: String) -> Double? {
        let parts = fractionStr.components(separatedBy: "/")
        guard parts.count == 2,
              let numerator = Double(parts[0]),
              let denominator = Double(parts[1]),
              denominator != 0 else {
            return nil
        }
        return numerator / denominator
    }
}

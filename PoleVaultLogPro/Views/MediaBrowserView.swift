import SwiftUI
import AVKit
import AVFoundation
import PhotosUI
import Photos

/// A view that displays media items for a jump with native Apple components
struct MediaBrowserView: View {
    let jump: Jump
    @State private var selectedMediaIndex: Int = 0
    @State private var mediaItems: [JumpMedia] = []
    @State private var isLoading = true
    @State private var errorMessage: String?
    @Environment(\.managedObjectContext) private var viewContext

    var body: some View {
        ZStack {
            // Background color
            Color.black.edgesIgnoringSafeArea(.all)

            if isLoading {
                // Loading indicator
                ProgressView()
                    .scaleEffect(1.5)
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
            } else if let errorMessage = errorMessage {
                // Error view
                VStack(spacing: 16) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 50))
                        .foregroundColor(.white)

                    Text("Error Loading Media")
                        .font(.headline)
                        .foregroundColor(.white)

                    Text(errorMessage)
                        .font(.subheadline)
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)

                    Button("Retry") {
                        loadMediaItems()
                    }
                    .padding()
                    .background(AppTheme.accentColor)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
                .padding()
            } else if mediaItems.isEmpty {
                // No media view
                VStack(spacing: 16) {
                    Image(systemName: "photo.on.rectangle.angled")
                        .font(.system(size: 50))
                        .foregroundColor(.white)

                    Text("No Media Available")
                        .font(.headline)
                        .foregroundColor(.white)

                    Text("This jump doesn't have any photos or videos attached.")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
                .padding()
            } else {
                // Media browser
                TabView(selection: $selectedMediaIndex) {
                    ForEach(Array(mediaItems.enumerated()), id: \.element.id) { index, media in
                        MediaItemView(media: media)
                            .tag(index)
                    }
                }
                .tabViewStyle(PageTabViewStyle())
                .indexViewStyle(PageIndexViewStyle(backgroundDisplayMode: .always))

                // Overlays
                VStack {
                    // Media counter overlay at top
                    HStack {
                        Spacer()
                        Text("\(selectedMediaIndex + 1) of \(mediaItems.count)")
                            .font(.caption)
                            .padding(8)
                            .background(Color.black.opacity(0.6))
                            .foregroundColor(.white)
                            .cornerRadius(8)
                            .padding()
                    }

                    Spacer()

                    // Jump details overlay at bottom (moved up by 10 pixels)
                    JumpDetailsOverlay(jump: jump)
                        .padding(.horizontal)
                        .padding(.bottom)
                        .offset(y: -10)
                }
            }
        }
        .navigationTitle("Media")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                if !mediaItems.isEmpty {
                    // Simple share button
                    Button(action: {
                        shareCurrentMedia()
                    }) {
                        Image(systemName: "square.and.arrow.up")
                    }
                }
            }
        }
        .onAppear {
            loadMediaItems()
        }
    }

    /// Loads media items for the jump
    private func loadMediaItems() {
        isLoading = true
        errorMessage = nil

        Task {
            // Get media items from the jump
            let items = jump.getMediaItems()

            // Filter out invalid media items
            var validItems: [JumpMedia] = []

            for media in items {
                // Check if media is available
                if media.isAvailable() {
                    // Convert JumpMediaStruct to JumpMedia
                    let jumpMedia = JumpMedia(context: viewContext)
                    jumpMedia.id = media.id.uuidString
                    jumpMedia.type = media.type.rawValue
                    jumpMedia.assetIdentifier = media.assetIdentifier
                    jumpMedia.posterTime = media.posterTime ?? 0.0
                    validItems.append(jumpMedia)
                }
            }

            // Update the UI on the main thread
            DispatchQueue.main.async {
                self.mediaItems = validItems
                self.isLoading = false

                if validItems.isEmpty {
                    self.errorMessage = "No valid media found for this jump."
                }
            }
        }
    }

    /// Shares the currently selected media using standard iOS sharing
    private func shareCurrentMedia() {
        guard selectedMediaIndex < mediaItems.count else { return }

        let media = mediaItems[selectedMediaIndex]

        // Show loading indicator
        isLoading = true
        errorMessage = "Preparing media for sharing..."

        // Prepare media in background using Task for async/await
        Task {
            // Prepare the media item
            var mediaItem: Any?

            // We only use assetIdentifier
            if let assetIdentifier = media.assetIdentifier {
                // First try to find the asset as a shared asset
                var asset = SharedMediaManager.shared.fetchSharedAsset(with: assetIdentifier)

                // If not found as a shared asset, try as a local asset
                if asset == nil {
                    let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [assetIdentifier], options: nil)
                    asset = fetchResult.firstObject
                }

                if let asset = asset {
                    if media.type == "photo" {
                        // For photos, get a UIImage
                        let options = PHImageRequestOptions()
                        options.isNetworkAccessAllowed = true
                        options.deliveryMode = .highQualityFormat
                        options.isSynchronous = true

                        var image: UIImage?
                        PHImageManager.default().requestImage(
                            for: asset,
                            targetSize: PHImageManagerMaximumSize,
                            contentMode: .aspectFit,
                            options: options
                        ) { img, _ in
                            image = img
                        }

                        mediaItem = image
                    }
                    else if media.type == "video" {
                        // For videos, use the PHAsset directly - iOS will handle sharing just like Photos app
                        mediaItem = asset
                    }
                }
            }

            // Update UI on main thread
            DispatchQueue.main.async {
                self.isLoading = false
                self.errorMessage = nil

                // Create activity items array with just the media
                var activityItems: [Any] = []

                // Add media if available
                if let mediaItem = mediaItem {
                    activityItems.append(mediaItem)

                    // Show share sheet
                    let activityVC = UIActivityViewController(
                        activityItems: activityItems,
                        applicationActivities: nil
                    )

                    // Configure activity view controller for iPad
                    if let popoverController = activityVC.popoverPresentationController {
                        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                           let window = windowScene.windows.first {
                            popoverController.sourceView = window
                            popoverController.sourceRect = CGRect(x: UIScreen.main.bounds.width / 2, y: UIScreen.main.bounds.height / 2, width: 0, height: 0)
                            popoverController.permittedArrowDirections = []
                        }
                    }

                    // Present the activity view controller
                    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                       let rootViewController = windowScene.windows.first?.rootViewController {
                        rootViewController.present(activityVC, animated: true)
                    }
                } else {
                    // Show error if media couldn't be prepared
                    self.errorMessage = "Could not prepare media for sharing."
                }
            }
        }
    }


}

/// A view that displays a single media item (photo or video)
struct MediaItemView: View {
    let media: JumpMedia
    @State private var isLoading = true
    @State private var errorMessage: String?

    var body: some View {
        ZStack {
            Color.black.edgesIgnoringSafeArea(.all)

            if isLoading {
                ProgressView()
                    .scaleEffect(1.5)
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
            } else if let errorMessage = errorMessage {
                VStack(spacing: 16) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 50))
                        .foregroundColor(.white)

                    Text("Error Loading Media")
                        .font(.headline)
                        .foregroundColor(.white)

                    Text(errorMessage)
                        .font(.subheadline)
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
                .padding()
            } else {
                if media.type == "photo" {
                    NativePhotoView(media: media)
                } else {
                    NativeVideoView(media: media)
                }
            }
        }
        .onAppear {
            // Validate the media when the view appears
            let isAvailable = media.isAvailable()

            DispatchQueue.main.async {
                self.isLoading = false

                if !isAvailable {
                    self.errorMessage = "Media not found. It may have been deleted or is no longer accessible."
                }
            }
        }
    }
}

/// A view that displays jump details as an overlay in the media browser
struct JumpDetailsOverlay: View {
    @ObservedObject var jump: Jump
    @State private var showTechnicalDetails = false

    var body: some View {
        VStack(spacing: 8) {
            // Main details card
            VStack(alignment: .leading, spacing: 6) {
                // Session title and type
                if let session = jump.session {
                    HStack {
                        Text(session.title ?? "Untitled Session")
                            .font(.headline)
                            .foregroundColor(.white)

                        Spacer()

                        Text(session.type?.capitalized ?? "Practice")
                            .font(.caption)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(sessionTypeColor.opacity(0.6))
                            .foregroundColor(.white)
                            .cornerRadius(4)
                    }
                }

                // Height, result, and attempt
                HStack {
                    Text(heightString)
                        .font(.subheadline)
                        .foregroundColor(.white)

                    Text(jump.result?.capitalized ?? "Unknown")
                        .font(.caption)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(resultColor.opacity(0.6))
                        .foregroundColor(.white)
                        .cornerRadius(4)

                    Text("Attempt \(jump.attemptIndex)")
                        .font(.caption)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(Color.gray.opacity(0.6))
                        .foregroundColor(.white)
                        .cornerRadius(4)

                    Spacer()

                    // Bar/Bungee indicator
                    HStack(spacing: 4) {
                        Text(jump.useBar ? "Bar" : "Bungee")
                            .font(.caption)
                            .foregroundColor(.white)

                        Image(systemName: jump.useBar ? "minus" : "water.waves")
                            .font(.caption)
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(jump.useBar ? Color.orange.opacity(0.6) : Color.blue.opacity(0.6))
                    .cornerRadius(4)
                }

                // Pole information (if available)
                if let pole = jump.pole, let poleName = pole.name {
                    HStack {
                        if let colorHex = pole.color, !colorHex.isEmpty {
                            Text(poleName)
                                .font(.caption)
                                .foregroundColor(Color(hex: colorHex))
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.black.opacity(0.4))
                                .cornerRadius(4)
                        } else {
                            Text(poleName)
                                .font(.caption)
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.black.opacity(0.4))
                                .cornerRadius(4)
                        }

                        Spacer()
                    }
                }

                // Comment if available
                if let comment = jump.comment, !comment.isEmpty {
                    Text(comment)
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.9))
                        .lineLimit(2)
                }

                // Technical details toggle
                Button(action: {
                    withAnimation {
                        showTechnicalDetails.toggle()
                    }
                }) {
                    HStack {
                        Text(showTechnicalDetails ? "Hide Technical Details" : "Show Technical Details")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.9))

                        Image(systemName: showTechnicalDetails ? "chevron.up" : "chevron.down")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.9))
                    }
                }
            }
            .padding()
            .background(Color.black.opacity(0.7))
            .cornerRadius(12)

            // Technical details (expandable)
            if showTechnicalDetails {
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            TechnicalDetailRow(label: "Run Start", value: formatTechnicalValue(jump.runStartCm))
                            TechnicalDetailRow(label: "Hand Hold", value: formatTechnicalValue(jump.handHoldCm))
                        }

                        Spacer()

                        VStack(alignment: .leading, spacing: 4) {
                            TechnicalDetailRow(label: "Take-Off Step", value: formatTechnicalValue(jump.takeOffStepCm))
                            TechnicalDetailRow(label: "Standard", value: formatTechnicalValue(jump.standardCm))
                        }
                    }
                }
                .padding()
                .background(Color.black.opacity(0.7))
                .cornerRadius(12)
            }
        }
    }

    // Helper view for technical details
    private struct TechnicalDetailRow: View {
        let label: String
        let value: String

        var body: some View {
            HStack(spacing: 4) {
                Text(label + ":")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))

                Text(value)
                    .font(.caption)
                    .foregroundColor(.white)
            }
        }
    }

    private var heightString: String {
        if AppTheme.useMetricSystem {
            return HeightConverter.cmToMetersString(jump.barHeightCm)
        } else {
            return HeightConverter.cmToFeetInchesString(jump.barHeightCm)
        }
    }

    private var resultColor: Color {
        switch jump.result {
        case "make":
            return .green
        case "miss":
            return .red
        case "pass":
            return .gray
        default:
            return .gray
        }
    }

    private var sessionTypeColor: Color {
        if let type = jump.session?.type?.lowercased() {
            return type == "meet" ? .orange : .blue
        }
        return .blue
    }

    /// Format technical value with appropriate units
    private func formatTechnicalValue(_ value: Double) -> String {
        if value < 0.1 {
            return "N/A"
        }

        if AppTheme.useMetricSystem {
            return String(format: "%.2f m", value / 100)
        } else {
            // Use appropriate formatter based on the measurement type
            if abs(value - jump.runStartCm) < 0.1 {
                return HeightConverter.formatStepMeasurement(cm: value)
            } else if abs(value - jump.handHoldCm) < 0.1 {
                return HeightConverter.formatHandHoldMeasurement(cm: value)
            } else if abs(value - jump.takeOffStepCm) < 0.1 {
                return HeightConverter.formatStepMeasurement(cm: value)
            } else {
                return HeightConverter.cmToFeetInchesString(value)
            }
        }
    }
}



import SwiftUI
import PhotosUI
import Photos
import AVKit

struct VideoSharingView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.managedObjectContext) private var viewContext

    // Input parameters
    let session: Session
    var jump: Jump? = nil

    // State variables
    @State private var selectedJump: Jump? = nil
    @State private var selectedVideoAsset: String? = nil
    @State private var isProcessing = false
    @State private var processingProgress: Float = 0
    @State private var processedVideoURL: URL? = nil
    @State private var errorMessage: String? = nil
    @State private var showShareSheet = false

    /// Wrapper for displaying the preview image in a sheet
    private struct IdentifiableImage: Identifiable, Equatable {
        let id = UUID()
        let image: UIImage
    }

    @State private var previewImage: IdentifiableImage?

    // Export options
    @State private var includeJumpStats = true
    @State private var includeSessionInfo = true
    @State private var exportQuality: ExportQuality = .auto
    @State private var useSimpleExport = false  // No overlays option

    // Enum for export quality options
    enum ExportQuality: String, CaseIterable, Identifiable {
        case auto = "Auto (Recommended)"
        case high = "High Quality"
        case medium = "Medium Quality"
        case low = "Low Quality (Fast)"

        var id: String { self.rawValue }

        var presetName: String {
            switch self {
            case .auto:
                return "auto"
            case .high:
                return AVAssetExportPresetHighestQuality
            case .medium:
                return AVAssetExportPresetMediumQuality
            case .low:
                return AVAssetExportPreset1280x720
            }
        }

        var videoQualityPreset: VideoProcessor.VideoQualityPreset {
            switch self {
            case .auto:
                return .auto
            case .high:
                return .highest
            case .medium:
                return .medium
            case .low:
                return .low
            }
        }
    }

    // Fetch jumps for this session
    @FetchRequest private var jumps: FetchedResults<Jump>

    // Initialize with custom fetch request for jumps
    init(session: Session, jump: Jump? = nil) {
        self.session = session
        self.jump = jump

        // Configure the fetch request for jumps
        _jumps = FetchRequest<Jump>(
            sortDescriptors: [
                NSSortDescriptor(keyPath: \Jump.order, ascending: false) // Most recent first
            ],
            predicate: NSPredicate(format: "session == %@", session)
        )
    }

    var body: some View {
        NavigationStack {
            Form {
                // Video selection section
                if jump == nil {
                    jumpSelectionSection
                }

                // Video preview section
                videoPreviewSection

                // Export options section
                exportOptionsSection

                // Action buttons section
                actionButtonsSection
            }
            .navigationTitle("Video Sharing")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                // If a jump was provided, use it
                if let jump = jump {
                    selectedJump = jump
                    // Find the first video for this jump
                    findFirstVideoForJump(jump)
                } else if let firstJump = jumps.first {
                    // Otherwise use the first jump from the session
                    selectedJump = firstJump
                    findFirstVideoForJump(firstJump)
                }
            }
            .sheet(isPresented: $showShareSheet) {
                if let url = processedVideoURL {
                    ShareSheet(activityItems: [url])
                }
            }
            .sheet(item: $previewImage) { item in
                NavigationStack {
                        VStack {
                            Text("Overlay Preview")
                                .font(.headline)
                                .padding()

                            Image(uiImage: item.image)
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(maxHeight: 500)
                                .cornerRadius(12)
                                .padding()

                            Text("This shows how the overlay will appear on your video")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .padding(.bottom)

                            Spacer()
                        }
                        .toolbar {
                            ToolbarItem(placement: .cancellationAction) {
                                Button("Close") {
                                    previewImage = nil
                                }
                            }
                        }
                    }
            }
            .alert(isPresented: Binding<Bool>(
                get: { errorMessage != nil },
                set: { if !$0 { errorMessage = nil } }
            )) {
                Alert(
                    title: Text("Error"),
                    message: Text(errorMessage ?? "An unknown error occurred"),
                    dismissButton: .default(Text("OK"))
                )
            }
        }
    }

    // MARK: - View Components

    private var jumpSelectionSection: some View {
        Section(header: Text("Select Jump")) {
            if jumps.isEmpty {
                Text("No jumps available for this session")
                    .foregroundColor(.secondary)
            } else {
                Picker("Jump", selection: $selectedJump) {
                    ForEach(jumps) { jump in
                        JumpPickerRow(jump: jump)
                            .tag(jump as Jump?)
                    }
                }
                .pickerStyle(.navigationLink)
                .onChange(of: selectedJump) { _, newJump in
                    if let jump = newJump {
                        findFirstVideoForJump(jump)
                    } else {
                        selectedVideoAsset = nil
                    }
                }
            }
        }
    }

    private var videoPreviewSection: some View {
        Section(header: Text("Video Preview")) {
            if isProcessing {
                VStack(spacing: 12) {
                    ProgressView(value: processingProgress, total: 1.0)
                        .progressViewStyle(LinearProgressViewStyle())

                    Text("Processing video: \(Int(processingProgress * 100))%")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding()
            } else if let assetIdentifier = selectedVideoAsset {
                VStack(spacing: 8) {
                    // Create a JumpMediaStruct for the MediaThumbnailView
                    MediaThumbnailView(
                        media: JumpMediaStruct(
                            type: .video,
                            assetIdentifier: assetIdentifier,
                            posterTime: 0.3
                        ),
                        size: CGSize(width: 300, height: 200),
                        showTypeIcon: true
                    )
                    .cornerRadius(8)

                    if let jump = selectedJump {
                        Text("Height: \(formatHeight(jump.barHeightCm)) • \(jump.result?.capitalized ?? "Unknown")")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.vertical, 8)
            } else {
                VStack(spacing: 12) {
                    Image(systemName: "video.slash")
                        .font(.system(size: 40))
                        .foregroundColor(.secondary)
                        .padding()

                    Text("No video available")
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .padding()
            }
        }
    }

    private var exportOptionsSection: some View {
        Section(header: Text("Export Options")) {
            Toggle("Include Jump Statistics", isOn: $includeJumpStats)
                .disabled(useSimpleExport)

            Toggle("Include Session Info", isOn: $includeSessionInfo)
                .disabled(useSimpleExport)

            Picker("Export Quality", selection: $exportQuality) {
                ForEach(ExportQuality.allCases) { quality in
                    Text(quality.rawValue).tag(quality)
                }
            }

            Toggle("No Overlays", isOn: $useSimpleExport)
                .foregroundColor(.orange)
                .onChange(of: useSimpleExport) { _, newValue in
                    if newValue {
                        // Disable stats and session info when no overlays is selected
                        includeJumpStats = false
                        includeSessionInfo = false
                    } else {
                        // Re-enable them when overlays are enabled
                        includeJumpStats = true
                        includeSessionInfo = true
                    }
                }

            // Information about watermark
            HStack {
                Image(systemName: "info.circle")
                    .foregroundColor(.blue)
                Text("App logo and name will always be included")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.top, 4)
        }
    }

    private var actionButtonsSection: some View {
        Section {
            // Preview button
            Button(action: generatePreview) {
                HStack {
                    Spacer()
                    Label("Preview Overlay", systemImage: "eye")
                    Spacer()
                }
            }
            .disabled(selectedVideoAsset == nil || useSimpleExport)
            .foregroundColor(.orange)

            Button(action: processAndShareVideo) {
                HStack {
                    Spacer()
                    Label("Process & Share Video", systemImage: "square.and.arrow.up")
                    Spacer()
                }
            }
            .disabled(selectedVideoAsset == nil || isProcessing)

            if processedVideoURL != nil {
                Button(action: {
                    showShareSheet = true
                }) {
                    HStack {
                        Spacer()
                        Label("Share Again", systemImage: "arrow.triangle.2.circlepath")
                        Spacer()
                    }
                }
                .foregroundColor(.blue)
            }
        }
    }

    // MARK: - Helper Methods

    /// Finds the first video for a jump
    private func findFirstVideoForJump(_ jump: Jump) {
        let mediaItems = jump.getMediaItems()
        let videoItem = mediaItems.first { $0.type == .video }

        selectedVideoAsset = videoItem?.assetIdentifier
    }

    /// Formats height using the app's height converter
    private func formatHeight(_ heightCm: Double) -> String {
        return HeightConverter.formatHeight(cm: heightCm)
    }

    /// Generates a preview of the overlay
    private func generatePreview() {
        guard let jump = selectedJump else { return }

        // Create overlay text
        let overlayText = buildOverlayText(for: jump)

        // For preview, we'll create a simple image showing what the overlay would look like
        let previewSize = CGSize(width: 400, height: 300)
        let renderer = UIGraphicsImageRenderer(size: previewSize)

        let image = renderer.image { context in
            // Background
            UIColor.black.setFill()
            context.fill(CGRect(origin: .zero, size: previewSize))

            // Container background
            let containerRect = CGRect(x: 20, y: previewSize.height - 120, width: previewSize.width - 40, height: 100)
            UIColor.black.withAlphaComponent(0.7).setFill()
            let path = UIBezierPath(roundedRect: containerRect, cornerRadius: 12)
            path.fill()

            // Text
            let attributes: [NSAttributedString.Key: Any] = [
                .foregroundColor: UIColor.white,
                .font: UIFont.systemFont(ofSize: 16, weight: .medium)
            ]

            let textRect = containerRect.insetBy(dx: 16, dy: 16)
            overlayText.draw(in: textRect, withAttributes: attributes)
        }

        previewImage = IdentifiableImage(image: image)
    }

    /// Processes and shares the video with overlays
    private func processAndShareVideo() {
        guard let assetIdentifier = selectedVideoAsset else { return }

        isProcessing = true
        processingProgress = 0.0
        errorMessage = nil

        if useSimpleExport {
            // Simple export without overlays - just use the watermark
            VideoProcessor.shared.processVideoWithWatermark(
                assetIdentifier: assetIdentifier,
                text: "PoleVaultLogPro"
            ) { result in
                DispatchQueue.main.async {
                    isProcessing = false
                    switch result {
                    case .success(let url):
                        processedVideoURL = url
                        showShareSheet = true
                    case .failure(let error):
                        errorMessage = "Failed to process video: \(error.localizedDescription)"
                    }
                }
            }
        } else {
            // Full export with overlays
            processVideoWithFullOverlays(assetIdentifier: assetIdentifier)
        }
    }

    /// Processes video with full overlays using VideoProcessor
    private func processVideoWithFullOverlays(assetIdentifier: String) {
        guard let jump = selectedJump else { return }

        // Create custom export options based on user selections
        var exportOptions = VideoProcessor.VideoExportOptions()
        exportOptions.includeJumpStats = includeJumpStats
        exportOptions.includeSessionInfo = includeSessionInfo
        exportOptions.includeAppWatermark = true // Always include watermark
        exportOptions.quality = exportQuality.videoQualityPreset

        // Use VideoProcessor to process with overlays
        VideoProcessor.shared.processVideoWithOverlay(
            assetIdentifier: assetIdentifier,
            jump: jump,
            options: exportOptions,
            progressHandler: { progress in
                DispatchQueue.main.async {
                    processingProgress = progress
                }
            }
        ) { result in
            DispatchQueue.main.async {
                isProcessing = false
                switch result {
                case .success(let url):
                    processedVideoURL = url
                    showShareSheet = true
                case .failure(let error):
                    errorMessage = "Failed to process video: \(error.localizedDescription)"
                }
            }
        }
    }

    /// Builds overlay text for preview purposes
    private func buildOverlayText(for jump: Jump) -> String {
        var components: [String] = []

        if includeJumpStats {
            // Height
            components.append("Height: \(formatHeight(jump.barHeightCm))")

            // Result
            if let result = jump.result {
                components.append("Result: \(result.capitalized)")
            }

            // Attempt number
            components.append("Attempt: \(jump.attemptIndex)")
        }

        if includeSessionInfo {
            // Session name
            if let title = session.title, !title.isEmpty {
                components.append(title)
            }

            // Date
            let date = Date(timeIntervalSinceReferenceDate: session.date)
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            components.append(formatter.string(from: date))
        }

        return components.joined(separator: "\n")
    }
}

// MARK: - Supporting Components

/// A row for displaying jump information in the picker
struct JumpPickerRow: View {
    let jump: Jump

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("Jump \(jump.order)")
                    .font(.headline)

                Text(HeightConverter.formatHeight(cm: jump.barHeightCm))
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }

            Spacer()

            VStack(alignment: .trailing, spacing: 4) {
                Text(jump.result?.capitalized ?? "Unknown")
                    .font(.subheadline)
                    .foregroundColor(resultColor)

                if hasVideo {
                    Image(systemName: "video.fill")
                        .font(.caption)
                        .foregroundColor(.blue)
                }
            }
        }
        .padding(.vertical, 4)
    }

    private var resultColor: Color {
        switch jump.result {
        case "make":
            return .green
        case "miss":
            return .red
        case "pass":
            return .orange
        default:
            return .gray
        }
    }

    private var hasVideo: Bool {
        let mediaItems = jump.getMediaItems()
        return mediaItems.contains { $0.type == .video }
    }
}

/// ShareSheet for presenting UIActivityViewController in SwiftUI
struct ShareSheet: UIViewControllerRepresentable {
    let activityItems: [Any]
    let applicationActivities: [UIActivity]? = nil

    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(
            activityItems: activityItems,
            applicationActivities: applicationActivities
        )
        return controller
    }

    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {
        // No updates needed
    }
}
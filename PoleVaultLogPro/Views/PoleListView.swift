import SwiftUI
import CoreData

struct PoleListView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss

    @FetchRequest private var poles: FetchedResults<Pole>

    @State private var showingAddPole = false
    @State private var selectedPole: Pole?
    @State private var showingDeleteAlert = false

    private let athlete: Athlete

    init(athlete: Athlete) {
        self.athlete = athlete

        // Initialize the fetch request for poles belonging to this athlete
        _poles = FetchRequest(
            fetchRequest: Pole.fetchRequest(
                in: PersistenceController.shared.container.viewContext,
                sortDescriptors: [NSSortDescriptor(keyPath: \Pole.order, ascending: true)],
                predicate: NSPredicate(format: "athlete == %@", athlete)
            )
        )
    }

    var body: some View {
        List {
            if poles.isEmpty {
                Text("No poles added yet. Tap the + button to add a pole.")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else {
                ForEach(poles) { pole in
                    HStack(spacing: 12) {
                        // Left side: Name and Brand
                        VStack(alignment: .leading, spacing: 4) {
                            // Name in the pole's color or black if no color
                            Button(action: {
                                selectedPole = pole
                            }) {
                                if let colorHex = pole.color, !colorHex.isEmpty {
                                    Text(pole.name ?? "")
                                        .font(.headline)
                                        .foregroundColor(Color(hex: colorHex))
                                } else {
                                    Text(pole.name ?? "")
                                        .font(.headline)
                                        .foregroundColor(.black)
                                }
                            }

                            if let brand = pole.brand, !brand.isEmpty {
                                Text(brand)
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                        }

                        Spacer()

                        // Right side: Length and Weight
                        VStack(alignment: .trailing, spacing: 4) {
                            Text(pole.getFormattedLength())
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Text(pole.getFormattedWeight())
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .frame(width: 100) // Fixed width for consistent layout
                    }
                    .contextMenu {
                        Button(action: {
                            selectedPole = pole
                        }) {
                            Label("Edit", systemImage: "pencil")
                        }

                        Button(role: .destructive, action: {
                            selectedPole = pole
                            showingDeleteAlert = true
                        }) {
                            Label("Delete", systemImage: "trash")
                        }
                    }
                }
                .onDelete(perform: deletePoles)
                .onMove(perform: movePoles)
            }
        }
        .navigationTitle("Poles")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    showingAddPole = true
                }) {
                    Label("Add Pole", systemImage: "plus")
                }
            }

            ToolbarItem(placement: .navigationBarTrailing) {
                EditButton()
            }
        }
        .sheet(isPresented: $showingAddPole) {
            PoleEditView(athlete: athlete)
        }
        .sheet(item: $selectedPole) { pole in
            PoleEditView(athlete: athlete, pole: pole)
        }
        .alert("Delete Pole", isPresented: $showingDeleteAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                if let pole = selectedPole {
                    deletePole(pole)
                }
            }
        } message: {
            Text("Are you sure you want to delete this pole? This action cannot be undone.")
        }
    }

    private func deletePoles(at offsets: IndexSet) {
        withAnimation {
            for index in offsets {
                let pole = poles[index]
                viewContext.delete(pole)
            }

            saveContext()
        }
    }

    private func deletePole(_ pole: Pole) {
        withAnimation {
            viewContext.delete(pole)
            saveContext()
        }
    }

    private func movePoles(from source: IndexSet, to destination: Int) {
        // Convert to array to make it mutable
        var poleArray = poles.map { $0 }

        // Perform the move
        poleArray.move(fromOffsets: source, toOffset: destination)

        // Update the order property of each pole
        for (index, pole) in poleArray.enumerated() {
            pole.order = Int16(index)
        }

        saveContext()
    }

    private func saveContext() {
        do {
            try viewContext.save()
        } catch {
            let nsError = error as NSError
            print("Error saving context: \(nsError), \(nsError.userInfo)")
        }
    }

    // Helper function to get a friendly color name from a hex string
    private func getFriendlyColorName(from hexColor: String) -> String {
        // Convert hex to Color
        let color = Color(hex: hexColor)

        // Convert to RGB components
        let uiColor = UIColor(color)
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0

        uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)

        // Simple color detection based on RGB values
        if red > 0.7 && green < 0.3 && blue < 0.3 {
            return "Red"
        } else if red < 0.3 && green > 0.6 && blue < 0.3 {
            return "Green"
        } else if red < 0.3 && green < 0.3 && blue > 0.7 {
            return "Blue"
        } else if red > 0.7 && green > 0.7 && blue < 0.3 {
            return "Yellow"
        } else if red > 0.7 && green > 0.4 && green < 0.7 && blue < 0.3 {
            return "Orange"
        } else if red > 0.5 && green < 0.3 && blue > 0.5 {
            return "Purple"
        } else if red < 0.2 && green < 0.2 && blue < 0.2 {
            return "Black"
        } else if red > 0.9 && green > 0.9 && blue > 0.9 {
            return "White"
        } else if red > 0.4 && red < 0.6 && green > 0.4 && green < 0.6 && blue > 0.4 && blue < 0.6 {
            return "Gray"
        } else if red > 0.7 && green < 0.5 && blue > 0.5 {
            return "Pink"
        }

        // Default to a generic name
        return "Custom"
    }
}

#Preview {
    let context = PersistenceController.preview.container.viewContext
    let athlete = Athlete.create(in: context, name: "Test Athlete")

    // Create sample poles
    let _ = Pole.create(in: context, athlete: athlete, name: "Competition", brand: "UCS Spirit", weight: 160, lengthCm: 457.2, color: "Yellow", order: 0)
    let _ = Pole.create(in: context, athlete: athlete, name: "Practice", brand: "Altius", weight: 150, lengthCm: 426.7, color: "Blue", order: 1)

    return NavigationStack {
        PoleListView(athlete: athlete)
            .environment(\.managedObjectContext, context)
    }
}

// Helper function to create sample poles for preview
private func createSamplePoles(context: NSManagedObjectContext, athlete: Athlete) -> Bool {
    // Create the poles and return true if successful
    _ = Pole.create(in: context, athlete: athlete, name: "Competition", brand: "UCS Spirit", weight: 160, lengthCm: 457.2, color: "Yellow", order: 0)
    _ = Pole.create(in: context, athlete: athlete, name: "Practice", brand: "Altius", weight: 150, lengthCm: 426.7, color: "Blue", order: 1)

    // Just return true since the create method doesn't return an optional
    return true
}

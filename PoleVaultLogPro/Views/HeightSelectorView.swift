import SwiftUI

/// A reusable height picker component that displays a single scrollable list of heights
/// with both metric and imperial units displayed according to user preference
struct HeightSelectorView: View {
    @Environment(\.dismiss) private var dismiss


    // Use the global AppTheme.useMetricSystem directly
    private var useMetric: Bool { AppTheme.useMetricSystem }
    @AppStorage("barHeightMinCm") private var barHeightMinCm: Double = 25 // 0.25m in cm
    @AppStorage("barHeightMaxCm") private var barHeightMaxCm: Double = 700 // 7.00m in cm

    @Binding var selectedHeight: Double
    let onSelect: (Double) -> Void
    let sessionType: String
    let heightStandard: String?

    /// Finds the closest height value in the provided array to the target height
    /// - Parameters:
    ///   - target: The target height to find the closest match for
    ///   - heights: Array of available heights
    /// - Returns: The closest height value from the array
    private func findClosestHeight(to target: Double, in heights: [Double]) -> Double {
        guard !heights.isEmpty else { return target }

        // If the target is in the array, return it directly
        if heights.contains(target) {
            return target
        }

        // Find the closest height by minimizing the absolute difference
        return heights.min(by: { abs($0 - target) < abs($1 - target) }) ?? target
    }

    // For practice sessions, we use a range of heights
    private var heightOptions: [Double] {
        // Use practice mode range for all sessions in V1.0
        var heights: [Double] = []
        let minHeightCm = max(barHeightMinCm, 25) // Minimum 0.25m
        let maxHeightCm = min(barHeightMaxCm, 700) // Maximum 7.00m

            if useMetric {
                // For metric, use 1cm increments
                for heightCm in stride(from: minHeightCm, through: maxHeightCm, by: 1) {
                    heights.append(heightCm)
                }
            } else {
                // For imperial, use quarter-inch increments
                // Convert min/max to inches, then iterate by 0.25 inches
                let minInches = minHeightCm / 2.54
                let maxInches = maxHeightCm / 2.54

                // Round min down to nearest quarter inch and max up to nearest quarter inch
                let roundedMinInches = (floor(minInches * 4) / 4)
                let roundedMaxInches = (ceil(maxInches * 4) / 4)

                for inches in stride(from: roundedMinInches, through: roundedMaxInches, by: 0.25) {
                    // Convert back to cm for storage
                    let heightCm = inches * 2.54
                    heights.append(heightCm)
                }
            }
        return heights
    }

    var body: some View {
        NavigationStack {
            VStack {
                Text("Select Height")
                    .font(.headline)
                    .padding(.top)

                ScrollViewReader { scrollProxy in
                    List {
                        ForEach(heightOptions, id: \.self) { heightCm in
                            Button(action: {
                                selectedHeight = heightCm
                                onSelect(heightCm)
                                dismiss()
                            }) {
                                HStack {
                                    if useMetric {
                                        Text(HeightConverter.cmToMetersString(heightCm))
                                            .fontWeight(.bold)
                                        Text("•")
                                        Text(HeightConverter.cmToFeetInchesString(heightCm))
                                            .foregroundColor(.secondary)
                                    } else {
                                        Text(HeightConverter.cmToFeetInchesString(heightCm))
                                            .fontWeight(.bold)
                                        Text("•")
                                        Text(HeightConverter.cmToMetersString(heightCm))
                                            .foregroundColor(.secondary)
                                    }

                                    Spacer()

                                    // Show a checkmark for the currently selected height
                                    if heightCm == selectedHeight {
                                        Image(systemName: "checkmark")
                                            .foregroundColor(.accentColor)
                                    }
                                }
                            }
                            .id(heightCm) // Use the height as the ID for scrolling
                        }
                    }
                    .onAppear {
                        // Find the closest height to the selected height
                        let closestHeight = findClosestHeight(to: selectedHeight, in: heightOptions)

                        // Scroll to the selected height with animation after a short delay
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            withAnimation {
                                scrollProxy.scrollTo(closestHeight, anchor: .center)
                            }
                        }
                    }
                }

                Button("Cancel") {
                    dismiss()
                }
                .padding()
            }
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

#Preview {
    HeightSelectorView(
        selectedHeight: .constant(300),
        onSelect: { _ in },
        sessionType: "practice",
        heightStandard: "Free-Range"
    )
}

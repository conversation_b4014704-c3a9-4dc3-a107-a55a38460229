// The Swift Programming Language
// https://docs.swift.org/swift-book

#if canImport(UIKit) && canImport(AVFoundation)
import UIKit
import AVFoundation
import Photos

/// Provides a simple way to burn a static watermark onto a video.
///
/// The helper fetches the video for a given ``<PERSON>HAsset`` and exports a new
/// file with the provided text rendered in the top‑right corner.
public enum WatermarkProcessor {

    /// Processes a ``PHAsset`` and exports a watermarked video.
    /// - Parameters:
    ///   - asset: The video asset from the photo library.
    ///   - text: The text to overlay on the video. Defaults to the app name.
    ///   - completion: Called with the URL to the processed video or an error.
    public static func applyWatermark(
        to asset: PHAsset,
        text: String = "PoleVaultLogPro",
        completion: @escaping (Result<URL, Error>) -> Void
    ) {
        let options = PHVideoRequestOptions()
        options.isNetworkAccessAllowed = true

        PHImageManager.default().requestAVAsset(forVideo: asset, options: options) { avAsset, _, info in
            if let urlAsset = avAsset as? AVURLAsset {
                let outputURL = FileManager.default.temporaryDirectory
                    .appendingPathComponent(UUID().uuidString)
                    .appendingPathExtension("mp4")

                do {
                    try process(url: urlAsset.url, text: text, outputURL: outputURL) { result in
                        DispatchQueue.main.async {
                            completion(result)
                        }
                    }
                } catch {
                    DispatchQueue.main.async {
                        completion(.failure(error))
                    }
                }
            } else if let error = info?[PHImageErrorKey] as? Error {
                DispatchQueue.main.async {
                    completion(.failure(error))
                }
            } else {
                DispatchQueue.main.async {
                    completion(.failure(NSError(domain: "WatermarkProcessor", code: -1)))
                }
            }
        }
    }

    // MARK: - Private helpers

    private static func process(
        url: URL,
        text: String,
        outputURL: URL,
        completion: @escaping (Result<URL, Error>) -> Void
    ) throws {
        let asset = AVURLAsset(url: url)

        guard let videoTrack = asset.tracks(withMediaType: .video).first else {
            throw NSError(domain: "WatermarkProcessor", code: -2)
        }

        let composition = AVMutableComposition()
        let compVideo = composition.addMutableTrack(
            withMediaType: .video,
            preferredTrackID: kCMPersistentTrackID_Invalid
        )!
        try compVideo.insertTimeRange(
            CMTimeRange(start: .zero, duration: asset.duration),
            of: videoTrack,
            at: .zero
        )

        if let audioTrack = asset.tracks(withMediaType: .audio).first {
            let compAudio = composition.addMutableTrack(
                withMediaType: .audio,
                preferredTrackID: kCMPersistentTrackID_Invalid
            )!
            try compAudio.insertTimeRange(
                CMTimeRange(start: .zero, duration: asset.duration),
                of: audioTrack,
                at: .zero
            )
        }

        let instruction = AVMutableVideoCompositionInstruction()
        instruction.timeRange = CMTimeRange(start: .zero, duration: asset.duration)

        let layerInstruction = AVMutableVideoCompositionLayerInstruction(assetTrack: compVideo)
        layerInstruction.setTransform(videoTrack.preferredTransform, at: .zero)
        instruction.layerInstructions = [layerInstruction]

        let videoComposition = AVMutableVideoComposition()
        videoComposition.instructions = [instruction]

        let frameRate = videoTrack.nominalFrameRate
        let timescale = frameRate > 0 ? Int32(frameRate) : 30
        videoComposition.frameDuration = CMTime(value: 1, timescale: timescale)

        // Determine render size accounting for orientation
        let naturalSize = videoTrack.naturalSize
        let isPortrait = abs(videoTrack.preferredTransform.b) == 1 && abs(videoTrack.preferredTransform.c) == 1
        videoComposition.renderSize = isPortrait ? CGSize(width: naturalSize.height, height: naturalSize.width) : naturalSize

        // Layers
        let videoLayer = CALayer()
        videoLayer.frame = CGRect(origin: .zero, size: videoComposition.renderSize)

        // Styled container similar to the media viewer overlay
        let containerLayer = CALayer()
        containerLayer.backgroundColor = UIColor.black
            .withAlphaComponent(0.6).cgColor
        containerLayer.cornerRadius = 6
        containerLayer.masksToBounds = true
        containerLayer.frame = CGRect(
            x: videoComposition.renderSize.width - 200,
            y: 20,
            width: 180,
            height: 40
        )

        let overlayLayer = CATextLayer()
        overlayLayer.string = text
        overlayLayer.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        overlayLayer.fontSize = 14
        overlayLayer.alignmentMode = .left
        overlayLayer.foregroundColor = UIColor.white
            .withAlphaComponent(0.9).cgColor
        overlayLayer.contentsScale = UIScreen.main.scale
        overlayLayer.frame = CGRect(
            x: 12,
            y: 10,
            width: 156,
            height: 20
        )
        containerLayer.addSublayer(overlayLayer)

        let parentLayer = CALayer()
        parentLayer.frame = CGRect(origin: .zero, size: videoComposition.renderSize)
        parentLayer.addSublayer(videoLayer)
        parentLayer.addSublayer(containerLayer)

        videoComposition.animationTool = AVVideoCompositionCoreAnimationTool(
            postProcessingAsVideoLayer: videoLayer,
            in: parentLayer
        )

        guard let exporter = AVAssetExportSession(
            asset: composition,
            presetName: AVAssetExportPresetHighestQuality
        ) else {
            throw NSError(domain: "WatermarkProcessor", code: -3)
        }
        exporter.outputURL = outputURL
        exporter.outputFileType = .mp4
        exporter.videoComposition = videoComposition

        exporter.exportAsynchronously {
            switch exporter.status {
            case .completed:
                completion(.success(outputURL))
            default:
                completion(.failure(exporter.error ?? NSError(domain: "WatermarkProcessor", code: -4)))
            }
        }
    }
}
#endif

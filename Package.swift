// swift-tools-version: 5.9
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
    name: "PoleVaultLogPro",
    platforms: [
        .iOS(.v16)
    ],
    products: [
        // Products define the executables and libraries a package produces, making them visible to other packages.
        .library(
            name: "PoleVaultLogPro",
            targets: ["PoleVaultLogPro"]),
    ],
    dependencies: [
        // Dependencies declare other packages that this package depends on.
    ],
    targets: [
        // Targets are the basic building blocks of a package, defining a module or a test suite.
        // Targets can depend on other targets in this package and products from dependencies.
        .target(
            name: "PoleVaultLogPro",
            dependencies: [],
            path: "PoleVaultLogPro",
            exclude: [
                "Info.plist",
                "PoleVaultLogPro.entitlements",
                "Assets.xcassets",
                "CloudKit"
            ],
            sources: [
                "Services",
                "Utilities", 
                "Model",
                "ViewModels"
            ]
        ),
        .testTarget(
            name: "PoleVaultLogProTests",
            dependencies: ["PoleVaultLogPro"],
            path: "PoleVaultLogProTests"
        ),
    ]
)
